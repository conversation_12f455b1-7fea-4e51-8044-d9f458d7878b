package com.xiang.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 用户注册响应DTO
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class RegisterResponse {
    private Long id;
    private String username;
    private String nickname;
    private String email;
    private String message;
    
    public static RegisterResponse success(Long id, String username, String nickname, String email) {
        return new RegisterResponse(id, username, nickname, email, "注册成功");
    }
}
