package com.xiang.chat.model.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 聊天消息DTO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ChatMessage {
    
    /**
     * 消息ID
     */
    private String messageId;
    
    /**
     * 发送者ID
     */
    private Long senderId;
    
    /**
     * 接收者ID（私聊时使用）
     */
    private Long receiverId;
    
    /**
     * 房间ID（群聊时使用）
     */
    private String roomId;
    
    /**
     * 消息类型
     * text: 文本消息
     * image: 图片消息
     * file: 文件消息
     * voice: 语音消息
     * video: 视频消息
     * system: 系统消息
     */
    private String messageType;
    
    /**
     * 消息内容
     */
    private String content;
    
    /**
     * 消息扩展数据（JSON格式）
     */
    private String extraData;
    
    /**
     * 发送时间
     */
    private Long sendTime;
    
    /**
     * 消息状态
     * 0: 发送中
     * 1: 已发送
     * 2: 已送达
     * 3: 已读
     * -1: 发送失败
     */
    private Integer status;
    
    /**
     * 引用的消息ID
     */
    private String referenceMessageId;
    
    /**
     * 引用的消息内容摘要（运行时动态获取，不存储到数据库）
     */
    private String referenceContent;
    
    /**
     * 引用消息的发送者ID（运行时动态获取，不存储到数据库）
     */
    private Long referenceSenderId;
    
    /**
     * 是否为私聊消息
     */
    public boolean isPrivateMessage() {
        return receiverId != null && receiverId > 0 &&
               (roomId == null || roomId.trim().isEmpty());
    }

    /**
     * 是否为群聊消息
     */
    public boolean isGroupMessage() {
        return roomId != null && !roomId.trim().isEmpty() &&
               (receiverId == null || receiverId <= 0);
    }

    /**
     * 是否为回复消息
     */
    public boolean isReplyMessage() {
        return referenceMessageId != null && !referenceMessageId.trim().isEmpty();
    }

    /**
     * 获取消息类型描述
     */
    public String getMessageTypeDescription() {
        if (isPrivateMessage()) {
            return "private";
        } else if (isGroupMessage()) {
            return "group";
        } else {
            return "unknown";
        }
    }
}
