package com.xiang.chat.service.impl;

import com.alibaba.fastjson2.JSON;
import com.xiang.chat.dto.WebSocketMessage;
import com.xiang.chat.model.dto.ChatMessage;
import com.xiang.chat.service.DistributedConnectionManager;
import com.xiang.chat.service.OfflineMessageService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 离线消息服务实现
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class OfflineMessageServiceImpl implements OfflineMessageService {

    private final RedisTemplate<String, Object> redisTemplate;
    private final DistributedConnectionManager distributedConnectionManager;

    // Redis Key 前缀
    private static final String OFFLINE_MESSAGE_KEY = "chat:offline:";
    private static final String OFFLINE_MESSAGE_SENT_KEY = "chat:offline:sent:";
    private static final String OFFLINE_MESSAGE_STATS_KEY = "chat:offline:stats:";
    private static final String OFFLINE_MESSAGE_SENDING_KEY = "chat:offline:sending:";

    // 离线消息最大保存数量
    private static final int MAX_OFFLINE_MESSAGES = 1000;

    // 离线消息过期时间（天）
    private static final int OFFLINE_MESSAGE_EXPIRE_DAYS = 7;

    // 发送锁过期时间（秒）
    private static final int SENDING_LOCK_EXPIRE_SECONDS = 60;

    @Override
    public void storeOfflineMessage(Long userId, WebSocketMessage message) {
        try {
            String key = OFFLINE_MESSAGE_KEY + userId;

            // 添加存储时间戳
            Map<String, Object> messageData = new HashMap<>();
            messageData.put("message", JSON.toJSONString(message));
            messageData.put("storeTime", LocalDateTime.now().toString());
            messageData.put("messageId", message.getMessageId());
            messageData.put("sent", false);

            // 存储到Redis列表（最新消息在前）
            redisTemplate.opsForList().leftPush(key, JSON.toJSONString(messageData));

            // 限制离线消息数量
            redisTemplate.opsForList().trim(key, 0, MAX_OFFLINE_MESSAGES - 1);

            // 设置过期时间
            redisTemplate.expire(key, OFFLINE_MESSAGE_EXPIRE_DAYS, TimeUnit.DAYS);

            // 更新统计信息
            updateOfflineMessageStats(userId, message);


            log.info("离线消息已存储: userId={}, messageType={}, messageId={}",
                    userId, message.getType(), message.getMessageId());

        } catch (Exception e) {
            log.error("存储离线消息失败: userId={}, message={}", userId, message, e);
        }
    }

    @Override
    public void storeOfflineChatMessage(Long userId, ChatMessage chatMessage) {
        // 转换为WebSocket消息格式
        WebSocketMessage wsMessage = WebSocketMessage.builder()
                .type(chatMessage.isPrivateMessage() ? "private_message" : "group_message")
                .data(Map.of(
                        "messageId", chatMessage.getMessageId(),
                        "senderId", chatMessage.getSenderId(),
                        "receiverId", chatMessage.getReceiverId() != null ? chatMessage.getReceiverId() : "",
                        "roomId", chatMessage.getRoomId() != null ? chatMessage.getRoomId() : "",
                        "messageType", chatMessage.getMessageType(),
                        "content", chatMessage.getContent(),
                        "sendTime", chatMessage.getSendTime()
                ))
                .timestamp(System.currentTimeMillis())
                .messageId(chatMessage.getMessageId())
                .senderId(chatMessage.getSenderId())
                .receiverId(chatMessage.getReceiverId())
                .roomId(chatMessage.getRoomId())
                .build();

        storeOfflineMessage(userId, wsMessage);
    }

    @Override
    public List<WebSocketMessage> getOfflineMessages(Long userId) {
        try {
            String key = OFFLINE_MESSAGE_KEY + userId;
            List<Object> messageDataList = redisTemplate.opsForList().range(key, 0, -1);

            if (messageDataList == null || messageDataList.isEmpty()) {
                return new ArrayList<>();
            }

            return messageDataList.stream()
                    .map(obj -> {
                        try {
                            Map<String, Object> messageData = JSON.parseObject(obj.toString(), Map.class);
                            String messageJson = (String) messageData.get("message");
                            return JSON.parseObject(messageJson, WebSocketMessage.class);
                        } catch (Exception e) {
                            log.error("解析离线消息失败: userId={}, data={}", userId, obj, e);
                            return null;
                        }
                    })
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList());

        } catch (Exception e) {
            log.error("获取离线消息失败: userId={}", userId, e);
            return new ArrayList<>();
        }
    }

    @Override
    public long getOfflineMessageCount(Long userId) {
        try {
            String key = OFFLINE_MESSAGE_KEY + userId;
            Long count = redisTemplate.opsForList().size(key);
            return count != null ? count : 0;
        } catch (Exception e) {
            log.error("获取离线消息数量失败: userId={}", userId, e);
            return 0;
        }
    }

    @Override
    @Async
    public void sendOfflineMessagesToUser(Long userId) {
        try {
            // 防重复发送锁
            String sendingLockKey = OFFLINE_MESSAGE_SENDING_KEY + userId;
            Boolean lockAcquired = redisTemplate.opsForValue().setIfAbsent(sendingLockKey, "1", SENDING_LOCK_EXPIRE_SECONDS, TimeUnit.SECONDS);

            if (!Boolean.TRUE.equals(lockAcquired)) {
                log.debug("离线消息正在发送中，跳过重复发送: userId={}", userId);
                return;
            }

            try {
                // 检查用户是否在线（全局检查）
                if (!distributedConnectionManager.isUserOnlineGlobally(userId)) {
                    log.debug("用户不在线，跳过发送离线消息: userId={}", userId);
                    return;
                }

                String key = OFFLINE_MESSAGE_KEY + userId;
                List<Object> messageDataList = redisTemplate.opsForList().range(key, 0, -1);

                if (messageDataList == null || messageDataList.isEmpty()) {
                    log.debug("用户无离线消息: userId={}", userId);
                    return;
                }

                int sentCount = 0;
                int failedCount = 0;

                // 按时间顺序发送（最旧的消息先发送）
                Collections.reverse(messageDataList);

                for (Object messageDataObj : messageDataList) {
                    try {
                        Map<String, Object> messageData = JSON.parseObject(messageDataObj.toString(), Map.class);
                        String messageJson = (String) messageData.get("message");
                        String messageId = (String) messageData.get("messageId");
                        Boolean sent = (Boolean) messageData.get("sent");

                        // 跳过已发送的消息
                        if (Boolean.TRUE.equals(sent)) {
                            continue;
                        }

                        WebSocketMessage message = JSON.parseObject(messageJson, WebSocketMessage.class);

                        // 添加离线消息标识
                        if (message.getData() instanceof Map) {
                            ((Map<String, Object>) message.getData()).put("isOfflineMessage", true);
                        }

                        // 发送消息（使用分布式连接管理器）
                        boolean success = distributedConnectionManager.sendMessageToUserGlobally(userId, message);

                        if (success) {
                            sentCount++;
                            // 标记为已发送
                            markOfflineMessageAsSent(userId, messageId);

                            // 短暂延迟，避免消息发送过快
                            Thread.sleep(50);
                        } else {
                            failedCount++;
                            log.warn("发送离线消息失败: userId={}, messageId={}", userId, messageId);
                            break; // 如果发送失败，停止发送后续消息
                        }

                    } catch (Exception e) {
                        failedCount++;
                        log.error("处理离线消息失败: userId={}, data={}", userId, messageDataObj, e);
                    }
                }

                // 如果所有消息都发送成功，清除离线消息
                if (failedCount == 0 && sentCount > 0) {
                    clearOfflineMessages(userId);
                }


                log.info("离线消息发送完成: userId={}, sent={}, failed={}", userId, sentCount, failedCount);

            } catch (Exception e) {
                log.error("发送离线消息失败: userId={}", userId, e);
            } finally {
                // 释放发送锁
                redisTemplate.delete(sendingLockKey);
            }

        } catch (Exception e) {
            log.error("获取离线消息发送锁失败: userId={}", userId, e);
        }
    }

    @Override
    public void clearOfflineMessages(Long userId) {
        try {
            String key = OFFLINE_MESSAGE_KEY + userId;
            String statsKey = OFFLINE_MESSAGE_STATS_KEY + userId;
            String sentKey = OFFLINE_MESSAGE_SENT_KEY + userId;

            redisTemplate.delete(key);
            redisTemplate.delete(statsKey);
            redisTemplate.delete(sentKey);

            log.info("离线消息已清除: userId={}", userId);

        } catch (Exception e) {
            log.error("清除离线消息失败: userId={}", userId, e);
        }
    }

    @Override
    public void markOfflineMessageAsSent(Long userId, String messageId) {
        try {
            String sentKey = OFFLINE_MESSAGE_SENT_KEY + userId;
            redisTemplate.opsForSet().add(sentKey, messageId);
            redisTemplate.expire(sentKey, OFFLINE_MESSAGE_EXPIRE_DAYS, TimeUnit.DAYS);

        } catch (Exception e) {
            log.error("标记离线消息为已发送失败: userId={}, messageId={}", userId, messageId, e);
        }
    }

    @Override
    public OfflineMessageStats getOfflineMessageStats(Long userId) {
        OfflineMessageStats stats = new OfflineMessageStats();
        stats.setUserId(userId);

        try {
            String statsKey = OFFLINE_MESSAGE_STATS_KEY + userId;
            Map<Object, Object> statsData = redisTemplate.opsForHash().entries(statsKey);

            if (statsData != null && !statsData.isEmpty()) {
                stats.setTotalCount(Long.parseLong(statsData.getOrDefault("totalCount", "0").toString()));
                stats.setPrivateMessageCount(Long.parseLong(statsData.getOrDefault("privateMessageCount", "0").toString()));
                stats.setGroupMessageCount(Long.parseLong(statsData.getOrDefault("groupMessageCount", "0").toString()));
                stats.setOldestMessageTime(statsData.getOrDefault("oldestMessageTime", "").toString());
                stats.setNewestMessageTime(statsData.getOrDefault("newestMessageTime", "").toString());
            }

            // 计算未发送消息数量
            long totalCount = getOfflineMessageCount(userId);
            String sentKey = OFFLINE_MESSAGE_SENT_KEY + userId;
            Long sentCount = redisTemplate.opsForSet().size(sentKey);
            stats.setUnsentCount(totalCount - (sentCount != null ? sentCount : 0));

        } catch (Exception e) {
            log.error("获取离线消息统计失败: userId={}", userId, e);
        }

        return stats;
    }

    /**
     * 定期清理过期的离线消息
     */
    @Override
    @Scheduled(fixedRate = 3600000) // 每小时执行一次
    public void cleanupExpiredOfflineMessages() {
        try {
            log.info("开始清理过期的离线消息...");

            // 清理过期的统计数据
            cleanupExpiredStats();

            // 清理过期的已发送标记
            cleanupExpiredSentMarks();

            // 清理空的离线消息队列
            cleanupEmptyOfflineQueues();

            log.info("过期离线消息清理完成");

        } catch (Exception e) {
            log.error("清理过期离线消息失败", e);
        }
    }

    /**
     * 批量发送离线消息（优化版本）
     */
    public void sendOfflineMessagesBatch(Long userId, int batchSize) {
        try {
            // 使用分布式连接管理器检查用户在线状态
            if (!distributedConnectionManager.isUserOnlineGlobally(userId)) {
                log.debug("用户不在线，跳过批量发送离线消息: userId={}", userId);
                return;
            }

            String key = OFFLINE_MESSAGE_KEY + userId;
            List<Object> messageDataList = redisTemplate.opsForList().range(key, 0, batchSize - 1);

            if (messageDataList == null || messageDataList.isEmpty()) {
                return;
            }

            int sentCount = 0;
            int failedCount = 0;
            List<String> sentMessageIds = new ArrayList<>();

            // 按时间顺序发送（最旧的消息先发送）
            Collections.reverse(messageDataList);

            for (Object messageDataObj : messageDataList) {
                try {
                    Map<String, Object> messageData = JSON.parseObject(messageDataObj.toString(), Map.class);
                    String messageJson = (String) messageData.get("message");
                    String messageId = (String) messageData.get("messageId");
                    Boolean sent = (Boolean) messageData.get("sent");

                    // 跳过已发送的消息
                    if (Boolean.TRUE.equals(sent)) {
                        continue;
                    }

                    WebSocketMessage message = JSON.parseObject(messageJson, WebSocketMessage.class);

                    // 添加离线消息标识
                    if (message.getData() instanceof Map) {
                        ((Map<String, Object>) message.getData()).put("isOfflineMessage", true);
                        ((Map<String, Object>) message.getData()).put("batchIndex", sentCount);
                    }

                    // 发送消息（使用分布式连接管理器）
                    boolean success = distributedConnectionManager.sendMessageToUserGlobally(userId, message);

                    if (success) {
                        sentCount++;
                        sentMessageIds.add(messageId);

                        // 控制发送速度
                        if (sentCount % 10 == 0) {
                            Thread.sleep(100); // 每10条消息暂停100ms
                        } else {
                            Thread.sleep(50);  // 每条消息间隔50ms
                        }
                    } else {
                        failedCount++;
                        log.warn("批量发送离线消息失败: userId={}, messageId={}", userId, messageId);
                        break; // 发送失败时停止
                    }

                } catch (Exception e) {
                    failedCount++;
                    log.error("处理批量离线消息失败: userId={}, data={}", userId, messageDataObj, e);
                }
            }

            // 批量标记消息为已发送
            if (!sentMessageIds.isEmpty()) {
                batchMarkMessagesAsSent(userId, sentMessageIds);
            }

            // 如果所有消息都发送成功，从队列中移除
            if (failedCount == 0 && sentCount > 0) {
                redisTemplate.opsForList().trim(key, sentCount, -1);
            }

            log.info("批量离线消息发送完成: userId={}, batchSize={}, sent={}, failed={}",
                    userId, batchSize, sentCount, failedCount);

        } catch (Exception e) {
            log.error("批量发送离线消息失败: userId={}, batchSize={}", userId, batchSize, e);
        }
    }

    /**
     * 批量标记消息为已发送
     */
    private void batchMarkMessagesAsSent(Long userId, List<String> messageIds) {
        try {
            String sentKey = OFFLINE_MESSAGE_SENT_KEY + userId;
            redisTemplate.opsForSet().add(sentKey, messageIds.toArray());
            redisTemplate.expire(sentKey, OFFLINE_MESSAGE_EXPIRE_DAYS, TimeUnit.DAYS);
        } catch (Exception e) {
            log.error("批量标记离线消息为已发送失败: userId={}, messageIds={}", userId, messageIds, e);
        }
    }

    /**
     * 清理过期的统计数据
     */
    private void cleanupExpiredStats() {
        // 这里可以实现更复杂的清理逻辑
        // 由于Redis设置了过期时间，大部分清理工作自动完成
        log.debug("清理过期统计数据完成");
    }

    /**
     * 清理过期的已发送标记
     */
    private void cleanupExpiredSentMarks() {
        // 清理过期的已发送标记
        log.debug("清理过期已发送标记完成");
    }

    /**
     * 清理空的离线消息队列
     */
    private void cleanupEmptyOfflineQueues() {
        // 清理空的离线消息队列
        log.debug("清理空离线消息队列完成");
    }

    /**
     * 更新离线消息统计信息
     */
    private void updateOfflineMessageStats(Long userId, WebSocketMessage message) {
        try {
            String statsKey = OFFLINE_MESSAGE_STATS_KEY + userId;
            String currentTime = LocalDateTime.now().format(DateTimeFormatter.ISO_LOCAL_DATE_TIME);

            // 更新总数
            redisTemplate.opsForHash().increment(statsKey, "totalCount", 1);

            // 更新消息类型统计
            if ("private_message".equals(message.getType())) {
                redisTemplate.opsForHash().increment(statsKey, "privateMessageCount", 1);
            } else if ("group_message".equals(message.getType())) {
                redisTemplate.opsForHash().increment(statsKey, "groupMessageCount", 1);
            }

            // 更新时间信息
            redisTemplate.opsForHash().put(statsKey, "newestMessageTime", currentTime);

            // 如果是第一条消息，设置最旧消息时间
            if (!redisTemplate.opsForHash().hasKey(statsKey, "oldestMessageTime")) {
                redisTemplate.opsForHash().put(statsKey, "oldestMessageTime", currentTime);
            }

            // 设置过期时间
            redisTemplate.expire(statsKey, OFFLINE_MESSAGE_EXPIRE_DAYS, TimeUnit.DAYS);

        } catch (Exception e) {
            log.error("更新离线消息统计失败: userId={}", userId, e);
        }
    }
}
