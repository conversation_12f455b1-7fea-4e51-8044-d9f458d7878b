package com.xiang.chat.feign;

import com.xiang.chat.code.R;
import com.xiang.chat.dto.RegisterRequest;
import com.xiang.chat.dto.RegisterResponse;
import com.xiang.chat.dto.UserInfo;
import com.xiang.chat.dto.ValidationResult;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;


@FeignClient(name = "auth-service", fallback = AuthFeignService.AuthFeignServiceFallback.class)
public interface AuthFeignService {

    /**
     * 验证JWT token
     */
    @PostMapping("/api/jwt/validate")
    R<ValidationResult> validateToken(@RequestParam("token") String token);

    /**
     * 获取用户信息
     */
    @GetMapping("/api/jwt/userinfo")
    R<UserInfo> getUserInfo();

    /**
     * 用户注册
     */
    @PostMapping("/api/register")
    R<RegisterResponse> register(@Valid @RequestBody RegisterRequest registerRequest);

    /**
     * 检查用户名是否可用
     */
    @GetMapping("/api/register/check-username")
    R<Boolean> checkUsername(@RequestParam("username") String username);

    /**
     * 检查邮箱是否可用
     */
    @GetMapping("/api/register/check-email")
    R<Boolean> checkEmail(@RequestParam("email") String email);

    /**
     * Fallback实现
     */
    class AuthFeignServiceFallback implements AuthFeignService {

        @Override
        public R<ValidationResult> validateToken(String token) {
            return R.error("认证服务不可用，请稍后重试");
        }

        @Override
        public R<UserInfo> getUserInfo() {
            return R.error("认证服务不可用");
        }

        @Override
        public R<RegisterResponse> register(RegisterRequest registerRequest) {
            return R.error("认证服务不可用，请稍后重试");
        }

        @Override
        public R<Boolean> checkUsername(String username) {
            return R.error("认证服务不可用，请稍后重试");
        }

        @Override
        public R<Boolean> checkEmail(String email) {
            return R.error("认证服务不可用，请稍后重试");
        }
    }
}
