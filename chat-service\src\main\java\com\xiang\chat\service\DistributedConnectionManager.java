package com.xiang.chat.service;

import com.xiang.chat.dto.WebSocketMessage;

/**
 * 分布式连接管理器接口
 * 支持多节点部署的聊天服务
 */
public interface DistributedConnectionManager {
    
    /**
     * 注册用户连接到当前节点
     */
    void registerUserConnection(Long userId, String nodeId);
    
    /**
     * 注销用户连接
     */
    void unregisterUserConnection(Long userId, String nodeId);
    
    /**
     * 检查用户是否在线（全局检查）
     */
    boolean isUserOnlineGlobally(Long userId);
    
    /**
     * 获取用户连接的节点ID
     */
    String getUserConnectionNode(Long userId);
    
    /**
     * 发送消息给用户（跨节点）
     */
    boolean sendMessageToUserGlobally(Long userId, WebSocketMessage message);
    
    /**
     * 广播消息到房间（跨节点）
     */
    int broadcastToRoomGlobally(String roomId, WebSocketMessage message, Long excludeUserId);
    
    /**
     * 获取全局在线用户数量
     */
    int getGlobalOnlineUserCount();
    
    /**
     * 获取当前节点ID
     */
    String getCurrentNodeId();
    
    /**
     * 处理来自其他节点的消息转发请求
     */
    void handleMessageForward(Long userId, WebSocketMessage message);
    
    /**
     * 用户加入房间（全局同步）
     */
    void joinRoomGlobally(Long userId, String roomId);
    
    /**
     * 用户离开房间（全局同步）
     */
    void leaveRoomGlobally(Long userId, String roomId);
    
    /**
     * 获取房间内所有用户（跨节点）
     */
    java.util.Set<Long> getRoomUsersGlobally(String roomId);
}
