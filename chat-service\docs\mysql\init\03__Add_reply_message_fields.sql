-- 添加回复消息相关字段
USE springcloud_chat;

-- 为chat_message表添加引用消息相关字段
ALTER TABLE chat_message 
ADD COLUMN reference_message_id VARCHAR(64) NULL COMMENT '引用的消息ID' AFTER status;

-- 为chat_message表添加索引以优化引用消息查询
ALTER TABLE chat_message 
ADD INDEX idx_reference_message_id (reference_message_id);

-- 修改消息类型约束以包含reply类型
ALTER TABLE chat_message 
DROP CONSTRAINT chk_message_type;

ALTER TABLE chat_message 
ADD CONSTRAINT chk_message_type CHECK (message_type IN ('text', 'image', 'file', 'voice', 'video', 'system', 'reply'));

-- 添加外键约束（可选，用于确保引用的消息存在）
-- ALTER TABLE chat_message 
-- ADD CONSTRAINT fk_reference_message 
-- FOREIGN KEY (reference_message_id) REFERENCES chat_message(message_id) ON DELETE SET NULL;