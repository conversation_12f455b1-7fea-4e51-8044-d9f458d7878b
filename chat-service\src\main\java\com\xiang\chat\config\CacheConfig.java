package com.xiang.chat.config;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.J<PERSON>NReader;
import com.alibaba.fastjson2.JSONWriter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.cache.CacheManager;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.data.redis.cache.RedisCacheConfiguration;
import org.springframework.data.redis.cache.RedisCacheManager;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.serializer.GenericJackson2JsonRedisSerializer;
import org.springframework.data.redis.serializer.RedisSerializationContext;
import org.springframework.data.redis.serializer.RedisSerializer;
import org.springframework.data.redis.serializer.StringRedisSerializer;

import java.nio.charset.StandardCharsets;
import java.time.Duration;
import java.util.HashMap;
import java.util.Map;

/**
 * 缓存配置
 */
@Slf4j
@Configuration
@EnableCaching
@ConditionalOnClass(RedisTemplate.class)
public class CacheConfig {

    /**
     * Redis模板配置 - 性能优化版本
     */
    @Bean
    @Primary
    public RedisTemplate<String, Object> redisTemplate(RedisConnectionFactory connectionFactory) {
        RedisTemplate<String, Object> template = new RedisTemplate<>();
        template.setConnectionFactory(connectionFactory);

        // 使用String序列化器作为key的序列化器
        StringRedisSerializer stringRedisSerializer = new StringRedisSerializer();
        template.setKeySerializer(stringRedisSerializer);
        template.setHashKeySerializer(stringRedisSerializer);

        // 使用FastJson2序列化器作为value的序列化器（性能更好）
        FastJson2RedisSerializer<Object> fastJson2RedisSerializer = new FastJson2RedisSerializer<>(Object.class);
        template.setValueSerializer(fastJson2RedisSerializer);
        template.setHashValueSerializer(fastJson2RedisSerializer);

        // 开启事务支持
        template.setEnableTransactionSupport(true);
        
        // 设置默认序列化器
        template.setDefaultSerializer(fastJson2RedisSerializer);
        
        template.afterPropertiesSet();
        
        log.info("Redis模板配置完成 - 已启用性能优化");
        return template;
    }

    /**
     * 缓存管理器配置
     */
    @Bean
    @Primary
    public CacheManager cacheManager(RedisConnectionFactory connectionFactory) {
        // 默认缓存配置
        RedisCacheConfiguration defaultConfig = RedisCacheConfiguration.defaultCacheConfig()
                .entryTtl(Duration.ofMinutes(30)) // 默认30分钟过期
                .serializeKeysWith(RedisSerializationContext.SerializationPair.fromSerializer(new StringRedisSerializer()))
                .serializeValuesWith(RedisSerializationContext.SerializationPair.fromSerializer(new GenericJackson2JsonRedisSerializer()))
                .disableCachingNullValues(); // 不缓存null值

        // 不同缓存的个性化配置
        Map<String, RedisCacheConfiguration> cacheConfigurations = new HashMap<>();
        
        // 用户缓存 - 1小时过期
        cacheConfigurations.put("user", defaultConfig.entryTtl(Duration.ofHours(1)));
        
        // 订单缓存 - 10分钟过期
        cacheConfigurations.put("order", defaultConfig.entryTtl(Duration.ofMinutes(10)));
        
        // 聊天室缓存 - 2小时过期
        cacheConfigurations.put("chatroom", defaultConfig.entryTtl(Duration.ofHours(2)));
        
        // 配置缓存 - 24小时过期
        cacheConfigurations.put("config", defaultConfig.entryTtl(Duration.ofHours(24)));
        
        // 短期缓存 - 5分钟过期
        cacheConfigurations.put("short", defaultConfig.entryTtl(Duration.ofMinutes(5)));
        
        // 长期缓存 - 7天过期
        cacheConfigurations.put("long", defaultConfig.entryTtl(Duration.ofDays(7)));

        RedisCacheManager cacheManager = RedisCacheManager.builder(connectionFactory)
                .cacheDefaults(defaultConfig)
                .withInitialCacheConfigurations(cacheConfigurations)
                .transactionAware() // 支持事务
                .build();

        log.info("Redis缓存管理器配置完成，支持缓存: {}", cacheConfigurations.keySet());
        return cacheManager;
    }

    /**
     * FastJson2 Redis序列化器
     */
    public static class FastJson2RedisSerializer<T> implements RedisSerializer<T> {
        
        private final Class<T> clazz;

        public FastJson2RedisSerializer(Class<T> clazz) {
            this.clazz = clazz;
        }

        @Override
        public byte[] serialize(T t) {
            if (t == null) {
                return new byte[0];
            }
            return JSON.toJSONString(t, JSONWriter.Feature.WriteClassName).getBytes(StandardCharsets.UTF_8);
        }

        @Override
        public T deserialize(byte[] bytes) {
            if (bytes == null || bytes.length == 0) {
                return null;
            }
            String str = new String(bytes, StandardCharsets.UTF_8);
            return JSON.parseObject(str, clazz, JSONReader.Feature.SupportAutoType);
        }
    }
}