package com.xiang.chat.service;

import com.xiang.chat.dto.WebSocketMessage;
import com.xiang.chat.model.dto.UserConnection;
import com.xiang.chat.model.AuthResult;
import io.netty.channel.Channel;

import java.util.List;
import java.util.Set;

/**
 * 连接管理器接口
 */
public interface ConnectionManager {
    
    /**
     * 添加连接
     */
    void addConnection(Channel channel, Long userId);
    
    /**
     * 移除连接
     */
    void removeConnection(Channel channel);
    
    /**
     * 根据用户ID获取连接
     */
    UserConnection getConnectionByUserId(Long userId);
    
    /**
     * 根据Channel获取连接
     */
    UserConnection getConnectionByChannel(Channel channel);
    
    /**
     * 根据Channel获取用户ID
     */
    Long getUserId(Channel channel);
    
    /**
     * 认证用户
     */
    Long authenticateUser(String token);

    /**
     * 认证用户并返回详细信息
     */
    AuthResult authenticateUserWithDetails(String token);

    /**
     * 获取用户连接数
     */
    int getUserConnectionCount(Long userId);
    
    /**
     * 用户加入房间
     */
    void joinRoom(Channel channel, String roomId);
    
    /**
     * 用户离开房间
     */
    void leaveRoom(Channel channel, String roomId);

    /**
     * 强制用户离开房间（管理员操作）
     */
    void removeUserFromRoom(String roomId, Long userId);

    /**
     * 获取房间内的所有用户
     */
    Set<Long> getRoomUsers(String roomId);
    
    /**
     * 获取用户加入的所有房间
     */
    Set<String> getUserRooms(Long userId);
    
    /**
     * 发送消息给指定用户
     */
    boolean sendMessageToUser(Long userId, WebSocketMessage message);
    
    /**
     * 广播消息到房间
     */
    int broadcastToRoom(String roomId, WebSocketMessage message, Long excludeUserId);
    
    /**
     * 广播消息到所有在线用户
     */
    int broadcastToAll(WebSocketMessage message, Long excludeUserId);
    
    /**
     * 获取在线用户数量
     */
    int getOnlineUserCount();
    
    /**
     * 获取所有在线用户ID
     */
    Set<Long> getOnlineUserIds();
    
    /**
     * 检查用户是否在线
     */
    boolean isUserOnline(Long userId);
    
    /**
     * 获取用户连接信息
     */
    List<UserConnection> getAllConnections();
    
    /**
     * 清理无效连接
     */
    void cleanupInactiveConnections();
    
    /**
     * 获取房间统计信息
     */
    RoomStats getRoomStats(String roomId);
    
    /**
     * 房间统计信息
     */
    class RoomStats {
        private String roomId;
        private int userCount;
        private long messageCount;
        private long createTime;
        
        // getters and setters
        public String getRoomId() { return roomId; }
        public void setRoomId(String roomId) { this.roomId = roomId; }
        public int getUserCount() { return userCount; }
        public void setUserCount(int userCount) { this.userCount = userCount; }
        public long getMessageCount() { return messageCount; }
        public void setMessageCount(long messageCount) { this.messageCount = messageCount; }
        public long getCreateTime() { return createTime; }
        public void setCreateTime(long createTime) { this.createTime = createTime; }
    }
}
