<!DOCTYPE html>
<html lang="zh-CN" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>聊天系统 - 注册</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .register-container {
            background: rgba(255, 255, 255, 0.95);
            padding: 40px;
            border-radius: 20px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
            width: 100%;
            max-width: 450px;
            backdrop-filter: blur(10px);
        }

        .register-header {
            text-align: center;
            margin-bottom: 30px;
        }

        .register-header h1 {
            color: #333;
            font-size: 28px;
            margin-bottom: 10px;
        }

        .register-header p {
            color: #666;
            font-size: 14px;
        }

        .register-form {
            display: flex;
            flex-direction: column;
            gap: 20px;
        }

        .form-group {
            display: flex;
            flex-direction: column;
        }

        .form-group label {
            color: #333;
            font-weight: 500;
            margin-bottom: 8px;
            font-size: 14px;
        }

        .form-group input {
            padding: 12px 16px;
            border: 2px solid #e1e5e9;
            border-radius: 10px;
            font-size: 16px;
            transition: all 0.3s ease;
            background: rgba(255, 255, 255, 0.8);
        }

        .form-group input:focus {
            outline: none;
            border-color: #667eea;
            background: rgba(255, 255, 255, 1);
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }

        .form-group input.error {
            border-color: #e74c3c;
        }

        .form-group .error-message {
            color: #e74c3c;
            font-size: 12px;
            margin-top: 5px;
        }

        .register-button {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 14px 20px;
            border: none;
            border-radius: 10px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            margin-top: 10px;
        }

        .register-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
        }

        .register-button:active {
            transform: translateY(0);
        }

        .register-button:disabled {
            background: #ccc;
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }

        .error-message {
            background: #fee;
            color: #c33;
            padding: 10px;
            border-radius: 5px;
            margin-bottom: 20px;
            text-align: center;
        }

        .success-message {
            background: #efe;
            color: #363;
            padding: 10px;
            border-radius: 5px;
            margin-bottom: 20px;
            text-align: center;
        }

        .login-link {
            text-align: center;
            margin-top: 20px;
            color: #666;
        }

        .login-link a {
            color: #667eea;
            text-decoration: none;
        }

        .login-link a:hover {
            text-decoration: underline;
        }

        .loading {
            display: none;
            text-align: center;
            margin-top: 10px;
        }

        .loading::after {
            content: '';
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 2px solid #667eea;
            border-radius: 50%;
            border-top-color: transparent;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            to {
                transform: rotate(360deg);
            }
        }
    </style>
</head>
<body>
    <div class="register-container">
        <div class="register-header">
            <h1>聊天系统</h1>
            <p>创建您的账号，开始聊天之旅</p>
        </div>

        <div id="error-message" class="error-message" style="display: none;"></div>
        <div id="success-message" class="success-message" style="display: none;"></div>

        <div class="register-form">
            <form id="registerForm">
                <div class="form-group">
                    <label for="username">用户名</label>
                    <input type="text" id="username" name="username" required minlength="3" maxlength="50">
                    <div class="error-message" id="username-error"></div>
                </div>

                <div class="form-group">
                    <label for="nickname">昵称</label>
                    <input type="text" id="nickname" name="nickname" required maxlength="100">
                    <div class="error-message" id="nickname-error"></div>
                </div>

                <div class="form-group">
                    <label for="email">邮箱（可选）</label>
                    <input type="email" id="email" name="email" maxlength="100">
                    <div class="error-message" id="email-error"></div>
                </div>

                <div class="form-group">
                    <label for="password">密码</label>
                    <input type="password" id="password" name="password" required minlength="6" maxlength="100">
                    <div class="error-message" id="password-error"></div>
                </div>

                <div class="form-group">
                    <label for="confirmPassword">确认密码</label>
                    <input type="password" id="confirmPassword" name="confirmPassword" required>
                    <div class="error-message" id="confirmPassword-error"></div>
                </div>

                <button type="submit" class="register-button" id="registerBtn">注册</button>
                <div class="loading" id="loading"></div>
            </form>
        </div>

        <div class="login-link">
            已有账号？<a href="/login">立即登录</a>
        </div>
    </div>

    <script>
        document.getElementById('registerForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const formData = new FormData(this);
            const registerData = {
                username: formData.get('username'),
                nickname: formData.get('nickname'),
                email: formData.get('email'),
                password: formData.get('password'),
                confirmPassword: formData.get('confirmPassword')
            };

            // 清除之前的错误信息
            clearErrors();
            
            // 前端验证
            if (!validateForm(registerData)) {
                return;
            }

            const registerBtn = document.getElementById('registerBtn');
            const loading = document.getElementById('loading');
            
            try {
                registerBtn.disabled = true;
                loading.style.display = 'block';
                
                const response = await fetch('/api/register', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(registerData)
                });

                const result = await response.json();
                
                if (result.success) {
                    showSuccess('注册成功！正在跳转到登录页面...');
                    setTimeout(() => {
                        window.location.href = '/login';
                    }, 2000);
                } else {
                    showError(result.message || '注册失败，请重试');
                }
            } catch (error) {
                console.error('Registration error:', error);
                showError('网络错误，请检查网络连接后重试');
            } finally {
                registerBtn.disabled = false;
                loading.style.display = 'none';
            }
        });

        function validateForm(data) {
            let isValid = true;
            
            // 验证用户名
            if (!data.username || data.username.length < 3 || data.username.length > 50) {
                showFieldError('username', '用户名长度必须在3-50个字符之间');
                isValid = false;
            }
            
            // 验证昵称
            if (!data.nickname || data.nickname.length > 100) {
                showFieldError('nickname', '昵称不能为空且长度不能超过100个字符');
                isValid = false;
            }
            
            // 验证邮箱（如果填写了）
            if (data.email && !isValidEmail(data.email)) {
                showFieldError('email', '邮箱格式不正确');
                isValid = false;
            }
            
            // 验证密码
            if (!data.password || data.password.length < 6 || data.password.length > 100) {
                showFieldError('password', '密码长度必须在6-100个字符之间');
                isValid = false;
            }
            
            // 验证确认密码
            if (data.password !== data.confirmPassword) {
                showFieldError('confirmPassword', '两次输入的密码不一致');
                isValid = false;
            }
            
            return isValid;
        }

        function isValidEmail(email) {
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            return emailRegex.test(email);
        }

        function showFieldError(fieldName, message) {
            const errorElement = document.getElementById(fieldName + '-error');
            const inputElement = document.getElementById(fieldName);
            
            if (errorElement) {
                errorElement.textContent = message;
            }
            if (inputElement) {
                inputElement.classList.add('error');
            }
        }

        function clearErrors() {
            const errorElements = document.querySelectorAll('.error-message');
            errorElements.forEach(element => {
                element.textContent = '';
            });
            
            const inputElements = document.querySelectorAll('input');
            inputElements.forEach(element => {
                element.classList.remove('error');
            });
            
            document.getElementById('error-message').style.display = 'none';
            document.getElementById('success-message').style.display = 'none';
        }

        function showError(message) {
            const errorElement = document.getElementById('error-message');
            errorElement.textContent = message;
            errorElement.style.display = 'block';
        }

        function showSuccess(message) {
            const successElement = document.getElementById('success-message');
            successElement.textContent = message;
            successElement.style.display = 'block';
        }

        // 实时验证用户名
        document.getElementById('username').addEventListener('blur', async function() {
            const username = this.value;
            if (username && username.length >= 3) {
                try {
                    const response = await fetch(`/api/register/check-username?username=${encodeURIComponent(username)}`);
                    const result = await response.json();
                    
                    if (!result.success) {
                        showFieldError('username', result.message || '检查用户名失败');
                    } else if (!result.data) {
                        showFieldError('username', '用户名已存在');
                    }
                } catch (error) {
                    console.error('Check username error:', error);
                }
            }
        });

        // 实时验证邮箱
        document.getElementById('email').addEventListener('blur', async function() {
            const email = this.value;
            if (email && isValidEmail(email)) {
                try {
                    const response = await fetch(`/api/register/check-email?email=${encodeURIComponent(email)}`);
                    const result = await response.json();
                    
                    if (!result.success) {
                        showFieldError('email', result.message || '检查邮箱失败');
                    } else if (!result.data) {
                        showFieldError('email', '邮箱已被使用');
                    }
                } catch (error) {
                    console.error('Check email error:', error);
                }
            }
        });
    </script>
</body>
</html>
