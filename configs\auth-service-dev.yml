server:
  port: 9000

spring:
  application:
    name: auth-service
    
  # 数据源配置
  datasource:
    type: com.alibaba.druid.pool.DruidDataSource
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: **************************************************************************************************************************************************************
    username: root
    password: 123456
    druid:
      initial-size: 5
      min-idle: 5
      max-active: 20
      max-wait: 60000
      
  # JPA配置
  jpa:
    hibernate:
      ddl-auto: update
    show-sql: false
    properties:
      hibernate:
        dialect: org.hibernate.dialect.MySQLDialect

# MyBatis Plus配置
mybatis-plus:
  configuration:
    map-underscore-to-camel-case: true
    log-impl: org.apache.ibatis.logging.slf4j.Slf4jImpl
  global-config:
    db-config:
      id-type: auto
      logic-delete-field: deleted
      logic-delete-value: 1
      logic-not-delete-value: 0

# OAuth2配置 - 仅支持密码模式
oauth2:
  jwt:
    # Token有效期(秒)
    access-token-validity: 3600
    refresh-token-validity: 86400
  client:
    # 客户端ID
    client-id: chat-client
    # 客户端密钥chat-secret
    client-secret: $2a$10$yGABu1Om/u0Hv3jkiSViSuPSwinfF3iXMSvW9rfYTMyFUscNdDyw2

# 日志配置
logging:
  level:
    com.xiang: DEBUG
    org.springframework.security: DEBUG
