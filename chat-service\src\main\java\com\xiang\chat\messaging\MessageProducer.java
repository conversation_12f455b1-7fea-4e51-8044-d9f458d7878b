package com.xiang.chat.messaging;

import com.alibaba.fastjson2.JSON;
import com.xiang.chat.constants.MessageConstants;
import com.xiang.chat.event.BaseEvent;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.client.producer.SendCallback;
import org.apache.rocketmq.client.producer.SendResult;
import org.apache.rocketmq.spring.core.RocketMQTemplate;
import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.messaging.Message;
import org.springframework.messaging.support.MessageBuilder;
import org.springframework.stereotype.Component;

/**
 * 消息生产者
 */
@Slf4j
@Component
@RequiredArgsConstructor
@ConditionalOnClass(RocketMQTemplate.class)
public class MessageProducer {

    private final RocketMQTemplate rocketMQTemplate;

    /**
     * 同步发送消息
     */
    public SendResult sendMessage(String topic, String tag, BaseEvent event) {
        return sendMessage(topic, tag, event, 0);
    }

    /**
     * 同步发送延迟消息
     */
    public SendResult sendMessage(String topic, String tag, BaseEvent event, int delayLevel) {
        try {

            // 构建消息
            Message<String> message = buildMessage(event, tag);
            String destination = topic + ":" + tag;
            
            SendResult result;
            if (delayLevel > 0) {
                result = rocketMQTemplate.syncSend(destination, message, 3000, delayLevel);
            } else {
                result = rocketMQTemplate.syncSend(destination, message);
            }
            

            log.info("同步发送消息成功: topic={}, tag={}, messageId={}, eventType={}", 
                topic, tag, result.getMsgId(), event.getEventType());
            
            return result;
        } catch (Exception e) {
            log.error("同步发送消息失败: topic={}, tag={}, eventType={}",
                topic, tag, event.getEventType(), e);
            throw new RuntimeException("发送消息失败", e);
        }
    }

    /**
     * 异步发送消息
     */
    public void sendMessageAsync(String topic, String tag, BaseEvent event, SendCallback callback) {
        sendMessageAsync(topic, tag, event, 0, callback);
    }

    /**
     * 异步发送延迟消息
     */
    public void sendMessageAsync(String topic, String tag, BaseEvent event, int delayLevel, SendCallback callback) {
        try {
            // 构建消息
            Message<String> message = buildMessage(event, tag);
            String destination = topic + ":" + tag;

            // 包装回调
            SendCallback wrappedCallback = new SendCallback() {
                @Override
                public void onSuccess(SendResult sendResult) {
                    log.info("异步发送消息成功: topic={}, tag={}, messageId={}, eventType={}",
                        topic, tag, sendResult.getMsgId(), event.getEventType());
                    
                    if (callback != null) {
                        callback.onSuccess(sendResult);
                    }
                }

                @Override
                public void onException(Throwable e) {

                    log.error("异步发送消息失败: topic={}, tag={}, eventType={}", 
                        topic, tag, event.getEventType(), e);
                    
                    if (callback != null) {
                        callback.onException(e);
                    }
                }
            };
            
            if (delayLevel > 0) {
                rocketMQTemplate.asyncSend(destination, message, wrappedCallback, 3000, delayLevel);
            } else {
                rocketMQTemplate.asyncSend(destination, message, wrappedCallback);
            }
            

        } catch (Exception e) {
            log.error("异步发送消息失败: topic={}, tag={}, eventType={}",
                topic, tag, event.getEventType(), e);
            throw new RuntimeException("发送消息失败", e);
        }
    }

    /**
     * 发送单向消息（不关心发送结果）
     */
    public void sendOneWay(String topic, String tag, BaseEvent event) {
        try {

            // 构建消息
            Message<String> message = buildMessage(event, tag);
            String destination = topic + ":" + tag;
            

            rocketMQTemplate.sendOneWay(destination, message);
            
            log.info("单向发送消息: topic={}, tag={}, eventType={}",
                topic, tag, event.getEventType());
            
        } catch (Exception e) {
            log.error("单向发送消息失败: topic={}, tag={}, eventType={}",
                topic, tag, event.getEventType(), e);
            throw new RuntimeException("发送消息失败", e);
        }
    }

    /**
     * 构建消息
     */
    private Message<String> buildMessage(BaseEvent event, String tag) {
        String messageBody = JSON.toJSONString(event);
        
        return MessageBuilder.withPayload(messageBody)
                .setHeader("TAGS", tag)
                .setHeader(MessageConstants.PROPERTY_EVENT_TYPE, event.getEventType())
                .setHeader(MessageConstants.PROPERTY_SOURCE, event.getSource())
                .setHeader(MessageConstants.PROPERTY_USER_ID, event.getUserId())
                .build();
    }
}
