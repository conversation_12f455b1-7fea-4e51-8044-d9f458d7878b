package com.xiang.chat.controller;

import com.xiang.chat.model.dto.ChatMessage;
import com.xiang.chat.model.dto.ChatRoomDTO;
import com.xiang.chat.model.entity.ChatMessageEntity;
import com.xiang.chat.model.entity.ChatRoomEntity;
import com.xiang.chat.result.Result;
import com.xiang.chat.service.ChatMessageService;
import com.xiang.chat.service.ChatRoomService;
import com.xiang.chat.service.ConnectionManager;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotBlank;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 聊天控制器
 */
@Slf4j
@RestController
@RequestMapping("/api/chat")
@RequiredArgsConstructor
@Validated
@Tag(name = "聊天管理", description = "聊天相关API")
public class ChatController {

    private final ChatMessageService chatMessageService;
    private final ChatRoomService chatRoomService;
    private final ConnectionManager connectionManager;

    @PostMapping("/rooms")
    @Operation(summary = "创建聊天室")
    public Result<ChatRoomEntity> createRoom(@Valid @RequestBody ChatRoomDTO roomDTO) {
        ChatRoomEntity room = chatRoomService.createRoom(roomDTO);
        return Result.success(room);
    }

    @GetMapping("/rooms/{roomId}")
    @Operation(summary = "获取聊天室信息")
    public Result<ChatRoomEntity> getRoom(
            @Parameter(description = "房间ID") @PathVariable String roomId) {
        ChatRoomEntity room = chatRoomService.getRoomById(roomId);
        if (room == null) {
            return Result.error("聊天室不存在");
        }
        return Result.success(room);
    }

    @PutMapping("/rooms/{roomId}")
    @Operation(summary = "更新聊天室信息")
    public Result<ChatRoomEntity> updateRoom(
            @Parameter(description = "房间ID") @PathVariable String roomId,
            @Valid @RequestBody ChatRoomDTO roomDTO) {
        ChatRoomEntity room = chatRoomService.updateRoom(roomId, roomDTO);
        return Result.success(room);
    }

    @DeleteMapping("/rooms/{roomId}")
    @Operation(summary = "删除聊天室")
    public Result<Void> deleteRoom(
            @Parameter(description = "房间ID") @PathVariable String roomId,
            @Parameter(description = "操作者ID") @RequestParam Long operatorId) {
        chatRoomService.deleteRoom(roomId, operatorId);
        return Result.success();
    }

    @GetMapping("/rooms/public")
    @Operation(summary = "获取公开聊天室列表")
    public Result<List<ChatRoomEntity>> getPublicRooms(
            @Parameter(description = "页码") @RequestParam(defaultValue = "1") @Min(1) int page,
            @Parameter(description = "页大小") @RequestParam(defaultValue = "20") @Min(1) @Max(100) int size) {
        List<ChatRoomEntity> rooms = chatRoomService.getPublicRooms(page, size);
        return Result.success(rooms);
    }

    @GetMapping("/rooms/search")
    @Operation(summary = "搜索聊天室")
    public Result<List<ChatRoomEntity>> searchRooms(
            @Parameter(description = "搜索关键词") @RequestParam @NotBlank String keyword,
            @Parameter(description = "页码") @RequestParam(defaultValue = "1") @Min(1) int page,
            @Parameter(description = "页大小") @RequestParam(defaultValue = "20") @Min(1) @Max(100) int size) {
        List<ChatRoomEntity> rooms = chatRoomService.searchRooms(keyword, page, size);
        return Result.success(rooms);
    }

    @GetMapping("/users/{userId}/rooms")
    @Operation(summary = "获取用户加入的聊天室")
    public Result<List<ChatRoomEntity>> getUserRooms(
            @Parameter(description = "用户ID") @PathVariable Long userId) {
        List<ChatRoomEntity> rooms = chatRoomService.getUserJoinedRooms(userId);
        return Result.success(rooms);
    }

    @PostMapping("/rooms/{roomId}/join")
    @Operation(summary = "加入聊天室")
    public Result<Void> joinRoom(
            @Parameter(description = "房间ID") @PathVariable String roomId,
            @Parameter(description = "用户ID") @RequestParam Long userId) {
        chatRoomService.joinRoom(roomId, userId);
        return Result.success();
    }

    @PostMapping("/rooms/{roomId}/leave")
    @Operation(summary = "离开聊天室")
    public Result<Void> leaveRoom(
            @Parameter(description = "房间ID") @PathVariable String roomId,
            @Parameter(description = "用户ID") @RequestParam Long userId) {
        chatRoomService.leaveRoom(roomId, userId);
        return Result.success();
    }

    @GetMapping("/rooms/{roomId}/members")
    @Operation(summary = "获取聊天室成员列表")
    public Result<List<Long>> getRoomMembers(
            @Parameter(description = "房间ID") @PathVariable String roomId) {
        List<Long> members = chatRoomService.getRoomMembers(roomId);
        return Result.success(members);
    }

    @GetMapping("/rooms/{roomId}/history")
    @Operation(summary = "获取聊天室历史消息")
    public Result<List<ChatMessageEntity>> getRoomHistory(
            @Parameter(description = "房间ID") @PathVariable String roomId,
            @Parameter(description = "用户ID") @RequestParam Long userId,
            @Parameter(description = "页码") @RequestParam(defaultValue = "1") @Min(1) int page,
            @Parameter(description = "页大小") @RequestParam(defaultValue = "50") @Min(1) @Max(100) int size) {
        List<ChatMessageEntity> messages = chatMessageService.getChatHistory(roomId, userId, page, size);
        return Result.success(messages);
    }

    @GetMapping("/users/{userId}/private-history")
    @Operation(summary = "获取私聊历史消息")
    public Result<List<ChatMessageEntity>> getPrivateHistory(
            @Parameter(description = "用户ID") @PathVariable Long userId,
            @Parameter(description = "对方用户ID") @RequestParam Long otherUserId,
            @Parameter(description = "页码") @RequestParam(defaultValue = "1") @Min(1) int page,
            @Parameter(description = "页大小") @RequestParam(defaultValue = "50") @Min(1) @Max(100) int size) {
        List<ChatMessageEntity> messages = chatMessageService.getPrivateChatHistory(userId, otherUserId, page, size);
        return Result.success(messages);
    }

    @GetMapping("/messages/{messageId}")
    @Operation(summary = "获取消息详情（包含引用信息）")
    public Result<ChatMessage> getMessageWithReference(
            @Parameter(description = "消息ID") @PathVariable String messageId) {
        ChatMessage message = chatMessageService.getMessageWithReferenceInfo(messageId);
        if (message == null) {
            return Result.error("消息不存在");
        }
        return Result.success(message);
    }

    @GetMapping("/messages/{messageId}/replies")
    @Operation(summary = "获取消息的回复列表")
    public Result<List<ChatMessageEntity>> getMessageReplies(
            @Parameter(description = "消息ID") @PathVariable String messageId,
            @Parameter(description = "页码") @RequestParam(defaultValue = "1") @Min(1) int page,
            @Parameter(description = "页大小") @RequestParam(defaultValue = "20") @Min(1) @Max(100) int size) {
        List<ChatMessageEntity> replies = chatMessageService.getMessageReplies(messageId, page, size);
        return Result.success(replies);
    }

    @PostMapping("/messages/{messageId}/read")
    @Operation(summary = "标记消息为已读")
    public Result<Void> markMessageAsRead(
            @Parameter(description = "消息ID") @PathVariable String messageId,
            @Parameter(description = "用户ID") @RequestParam Long userId) {
        chatMessageService.markMessageAsRead(messageId, userId);
        return Result.success();
    }

    @GetMapping("/users/{userId}/unread-count")
    @Operation(summary = "获取未读消息数量")
    public Result<Long> getUnreadCount(
            @Parameter(description = "用户ID") @PathVariable Long userId) {
        long count = chatMessageService.getUnreadMessageCount(userId);
        return Result.success(count);
    }

    @DeleteMapping("/messages/{messageId}")
    @Operation(summary = "删除消息")
    public Result<Void> deleteMessage(
            @Parameter(description = "消息ID") @PathVariable String messageId,
            @Parameter(description = "用户ID") @RequestParam Long userId) {
        chatMessageService.deleteMessage(messageId, userId);
        return Result.success();
    }

    @PostMapping("/messages/{messageId}/recall")
    @Operation(summary = "撤回消息")
    public Result<Void> recallMessage(
            @Parameter(description = "消息ID") @PathVariable String messageId,
            @Parameter(description = "用户ID") @RequestParam Long userId) {
        chatMessageService.recallMessage(messageId, userId);
        return Result.success();
    }

    @GetMapping("/online-users")
    @Operation(summary = "获取在线用户列表")
    public Result<Set<Long>> getOnlineUsers() {
        Set<Long> onlineUsers = connectionManager.getOnlineUserIds();
        return Result.success(onlineUsers);
    }

    @GetMapping("/online-count")
    @Operation(summary = "获取在线用户数量")
    public Result<Integer> getOnlineCount() {
        int count = connectionManager.getOnlineUserCount();
        return Result.success(count);
    }

    @GetMapping("/users/{userId}/status")
    @Operation(summary = "检查用户在线状态")
    public Result<Map<String, Object>> getUserStatus(
            @Parameter(description = "用户ID") @PathVariable Long userId) {
        boolean isOnline = connectionManager.isUserOnline(userId);
        Set<String> rooms = connectionManager.getUserRooms(userId);
        
        Map<String, Object> status = Map.of(
                "userId", userId,
                "isOnline", isOnline,
                "joinedRooms", rooms,
                "roomCount", rooms.size()
        );
        
        return Result.success(status);
    }

    @GetMapping("/rooms/{roomId}/stats")
    @Operation(summary = "获取聊天室统计信息")
    public Result<ConnectionManager.RoomStats> getRoomStats(
            @Parameter(description = "房间ID") @PathVariable String roomId) {
        ConnectionManager.RoomStats stats = connectionManager.getRoomStats(roomId);
        return Result.success(stats);
    }
}
