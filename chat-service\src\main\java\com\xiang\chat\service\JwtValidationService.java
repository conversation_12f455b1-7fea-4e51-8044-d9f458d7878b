package com.xiang.chat.service;

import com.xiang.chat.code.R;
import com.xiang.chat.dto.UserInfo;
import com.xiang.chat.dto.ValidationResult;
import com.xiang.chat.feign.AuthFeignService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * JWT验证服务
 * 使用Feign客户端调用auth-service验证JWT token
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class JwtValidationService {

    private final AuthFeignService authFeignService;

    /**
     * 验证JWT token并获取用户信息
     */
    public R<UserInfo> validateToken(String token) {
        try {
            if (token == null || token.trim().isEmpty()) {
                return R.error("Token不能为空");
            }

            // 通过Feign客户端调用auth-service验证接口
            R<ValidationResult> response = authFeignService.validateToken(token);

            if (response.isSuccess() && response.getData() != null) {
                ValidationResult validationResult = response.getData();
                UserInfo userInfo = validationResult.getUser();
                if (userInfo != null) {
                    return R.success(userInfo);
                }
            }

            // 如果验证失败，返回错误信息
            return R.error(response.getMessage() != null ? response.getMessage() : "Token验证失败");

        } catch (Exception e) {
            log.error("JWT validation error via Feign", e);
            return R.error("Token验证异常: " + e.getMessage());
        }
    }

    /**
     * 获取当前用户信息
     */
    public R<UserInfo> getCurrentUser() {
        try {
            // 通过Feign客户端调用auth-service获取用户信息接口
            R<UserInfo> response = authFeignService.getUserInfo();

            if (response.isSuccess() && response.getData() != null) {
                return R.success(response.getData());
            }

            // 如果获取失败，返回错误信息
            return R.error(response.getMessage() != null ? response.getMessage() : "获取用户信息失败");

        } catch (Exception e) {
            log.error("Get current user error via Feign", e);
            return R.error("获取用户信息异常: " + e.getMessage());
        }
    }

}