# 聊天微服务测试页面部署说明

## 📋 概述

聊天测试页面已成功集成到Spring Boot项目中，作为项目的首页提供完整的聊天功能测试界面。

## 🚀 访问方式

### 主要访问地址
- **首页**: http://localhost:8083/
- **聊天页面**: http://localhost:8083/chat
- **测试页面**: http://localhost:8083/test

### API文档地址
- **Knife4j文档**: http://localhost:8083/doc.html
- **Swagger UI**: http://localhost:8083/swagger-ui/index.html

## 🔧 项目配置

### 1. 依赖配置
已在 `chat-service/pom.xml` 中添加了Thymeleaf依赖：
```xml
<dependency>
    <groupId>org.springframework.boot</groupId>
    <artifactId>spring-boot-starter-thymeleaf</artifactId>
</dependency>
```

### 2. Thymeleaf配置
在 `configs/chat-service-dev.yml` 中配置了Thymeleaf：
```yaml
spring:
  thymeleaf:
    prefix: classpath:/templates/
    suffix: .html
    mode: HTML
    encoding: UTF-8
    cache: false  # 开发环境关闭缓存
    servlet:
      content-type: text/html
```

### 3. 视图控制器配置
在 `WebConfig` 中配置了视图控制器处理首页请求：
- `/` - 首页
- `/chat` - 聊天页面（别名）
- `/test` - 测试页面（别名）

### 4. Web配置
创建了 `WebConfig` 配置类：
- CORS跨域支持
- 静态资源处理
- 文件上传目录映射

## 📁 文件结构

```
chat-service/
├── src/main/resources/
│   ├── templates/
│   │   └── index.html          # 聊天测试页面
│   └── static/                 # 静态资源目录
├── src/main/java/com/example/chat/controller/
│   ├── ChatController.java     # 聊天API控制器
│   ├── FileController.java     # 文件上传控制器
│   └── ...
└── src/main/java/com/example/chat/config/
    └── WebConfig.java          # Web配置类
```

## 🎯 功能特性

### 页面功能
- **用户认证**: 支持多种Token格式认证
- **WebSocket连接**: 实时连接状态监控
- **聊天功能**: 私聊和群聊消息发送
- **房间管理**: 加入/离开房间，房间列表管理
- **API测试**: 内置API调用测试功能
- **日志监控**: 实时操作日志和调试信息

### 技术特性
- **响应式设计**: 适配不同屏幕尺寸
- **实时通信**: 基于WebSocket的实时消息传输
- **动态配置**: 自动获取服务器地址和端口
- **错误处理**: 完善的异常捕获和用户提示
- **跨域支持**: 支持跨域API调用

## 🔍 使用说明

### 1. 启动服务
确保以下服务已启动：
```bash
# 必需服务
- 聊天服务: http://localhost:8083
- WebSocket服务: ws://localhost:9090/ws
- Nacos注册中心: http://*************:8848
- Redis缓存: *************:6379
- MySQL数据库: *************:3306
```

### 2. 访问首页
打开浏览器访问: http://localhost:8083/

### 3. 用户认证
- 用户ID: 输入测试用户ID（如：1001）
- 认证Token: 输入对应Token（如：user_1001）
- 点击"连接WebSocket"进行认证

### 4. 测试聊天
- 加入房间：输入房间ID（如：room_001）
- 发送消息：输入消息内容并发送
- 私聊测试：指定接收者ID进行私聊

## 🛠️ 开发说明

### 模板引擎
使用Thymeleaf作为模板引擎：
- 模板文件位置: `src/main/resources/templates/`
- 支持Thymeleaf语法和表达式
- 开发环境禁用缓存，修改后刷新即可看到效果

### 静态资源
静态资源处理：
- CSS/JS文件: `src/main/resources/static/`
- 文件上传: `./uploads/` 目录
- 访问路径: `/static/**` 和 `/files/**`

### 跨域配置
已配置CORS支持：
- 允许所有域名访问
- 支持所有HTTP方法
- 允许携带认证信息

## 🔧 自定义配置

### 修改端口
在 `configs/chat-service-dev.yml` 中修改：
```yaml
server:
  port: 8083  # HTTP服务端口

netty:
  websocket:
    port: 9090  # WebSocket服务端口
```

### 修改模板
直接编辑 `src/main/resources/templates/index.html` 文件，保存后刷新浏览器即可看到效果。

### 添加新页面
1. 在 `templates/` 目录下创建新的HTML文件
2. 在 `WebConfig` 的 `addViewControllers` 方法中添加对应的视图映射
3. 重启服务即可访问

## 📝 注意事项

1. **开发环境**: Thymeleaf缓存已禁用，修改模板后刷新即可
2. **生产环境**: 建议启用Thymeleaf缓存以提高性能
3. **安全认证**: 当前使用简化的测试Token，生产环境请使用标准JWT
4. **跨域配置**: 生产环境建议限制允许的域名
5. **文件上传**: 确保上传目录有写入权限

## 🎉 总结

聊天测试页面已成功集成到Spring Boot项目中，提供了完整的Web界面来测试聊天微服务的所有功能。页面支持实时聊天、房间管理、API测试等功能，是开发和演示的理想工具。

访问 http://localhost:8083/ 即可开始使用！
