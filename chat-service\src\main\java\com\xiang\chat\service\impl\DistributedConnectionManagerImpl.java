package com.xiang.chat.service.impl;

import com.xiang.chat.constants.MessageConstants;
import com.xiang.chat.dto.WebSocketMessage;
import com.xiang.chat.event.MessageForwardEvent;
import com.xiang.chat.event.UserStatusChangedEvent;
import com.xiang.chat.messaging.MessageProducer;
import com.xiang.chat.service.ConnectionManager;
import com.xiang.chat.service.DistributedConnectionManager;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import javax.annotation.PreDestroy;
import java.net.InetAddress;
import java.util.Set;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;

/**
 * 分布式连接管理器实现
 * 基于Redis + RocketMQ实现跨节点通信
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class DistributedConnectionManagerImpl implements DistributedConnectionManager {

    private final RedisTemplate<String, Object> redisTemplate;
    private final ConnectionManager localConnectionManager;
    private final MessageProducer messageProducer;

    @Value("${spring.application.name:chat-service}")
    private String serviceName;

    @Value("${server.port:8083}")
    private String serverPort;

    private String currentNodeId;
    private ScheduledExecutorService scheduledExecutor;

    // Redis Key 前缀
    private static final String USER_NODE_KEY = "chat:user:node:";
    private static final String NODE_USERS_KEY = "chat:node:users:";
    private static final String ROOM_USERS_GLOBAL_KEY = "chat:room:users:global:";
    private static final String ONLINE_NODES_KEY = "chat:nodes:online";

    @PostConstruct
    public void init() {
        try {
            // 生成当前节点ID
            String hostName = InetAddress.getLocalHost().getHostName();
            currentNodeId = serviceName + "-" + hostName + "-" + serverPort + "-" + System.currentTimeMillis();
            
            // 注册当前节点
            registerCurrentNode();
            
            log.info("分布式连接管理器初始化完成，节点ID: {}", currentNodeId);
        } catch (Exception e) {
            log.error("分布式连接管理器初始化失败", e);
            throw new RuntimeException("分布式连接管理器初始化失败", e);
        }
    }

    @Override
    public void registerUserConnection(Long userId, String nodeId) {
        try {
            // 1. 记录用户连接的节点
            String userNodeKey = USER_NODE_KEY + userId;
            redisTemplate.opsForValue().set(userNodeKey, nodeId, 1, TimeUnit.HOURS);
            
            // 2. 将用户添加到节点的用户列表
            String nodeUsersKey = NODE_USERS_KEY + nodeId;
            redisTemplate.opsForSet().add(nodeUsersKey, userId.toString());
            redisTemplate.expire(nodeUsersKey, 1, TimeUnit.HOURS);
            
            // 3. 发送用户上线事件
            log.info("开始发送用户上线事件: userId={}, nodeId={}", userId, nodeId);
            sendUserStatusEvent(userId, "ONLINE", nodeId);
            log.info("用户上线事件发送完成: userId={}, nodeId={}", userId, nodeId);
            
            log.info("用户连接已注册到分布式管理器: userId={}, nodeId={}", userId, nodeId);
            
        } catch (Exception e) {
            log.error("注册用户连接失败: userId={}, nodeId={}", userId, nodeId, e);
        }
    }

    @Override
    public void unregisterUserConnection(Long userId, String nodeId) {
        try {
            // 1. 删除用户节点映射
            String userNodeKey = USER_NODE_KEY + userId;
            redisTemplate.delete(userNodeKey);
            
            // 2. 从节点用户列表中移除
            String nodeUsersKey = NODE_USERS_KEY + nodeId;
            redisTemplate.opsForSet().remove(nodeUsersKey, userId.toString());
            
            // 3. 发送用户下线事件
            sendUserStatusEvent(userId, "OFFLINE", nodeId);
            
            log.info("用户连接已从分布式管理器注销: userId={}, nodeId={}", userId, nodeId);
            
        } catch (Exception e) {
            log.error("注销用户连接失败: userId={}, nodeId={}", userId, nodeId, e);
        }
    }

    @Override
    public boolean isUserOnlineGlobally(Long userId) {
        try {
            String userNodeKey = USER_NODE_KEY + userId;
            String nodeId = (String) redisTemplate.opsForValue().get(userNodeKey);
            return nodeId != null;
        } catch (Exception e) {
            log.error("检查用户全局在线状态失败: userId={}", userId, e);
            return false;
        }
    }

    @Override
    public String getUserConnectionNode(Long userId) {
        try {
            String userNodeKey = USER_NODE_KEY + userId;
            return (String) redisTemplate.opsForValue().get(userNodeKey);
        } catch (Exception e) {
            log.error("获取用户连接节点失败: userId={}", userId, e);
            return null;
        }
    }

    @Override
    public boolean sendMessageToUserGlobally(Long userId, WebSocketMessage message) {
        try {
            // 1. 检查用户是否在当前节点
            if (localConnectionManager.isUserOnline(userId)) {
                // 用户在当前节点，直接发送
                boolean sent = localConnectionManager.sendMessageToUser(userId, message);
                return sent;
            }
            
            // 2. 检查用户是否在其他节点
            String targetNodeId = getUserConnectionNode(userId);
            if (targetNodeId == null) {
                // 用户不在线
                return false;
            }
            
            // 3. 通过消息队列转发到目标节点
            sendMessageForwardRequest(targetNodeId, userId, message);
            return true;
            
        } catch (Exception e) {
            log.error("全局发送消息失败: userId={}, message={}", userId, message, e);
            return false;
        }
    }

    @Override
    public int broadcastToRoomGlobally(String roomId, WebSocketMessage message, Long excludeUserId) {
        try {
            // 1. 先在当前节点广播
            int localSentCount = localConnectionManager.broadcastToRoom(roomId, message, excludeUserId);
            
            // 2. 通过消息队列通知其他节点广播
            sendRoomBroadcastRequest(roomId, message, excludeUserId);
            
            log.info("全局房间广播: roomId={}, localSent={}", roomId, localSentCount);
            return localSentCount; // 返回本地发送数量，总数量难以准确统计
            
        } catch (Exception e) {
            log.error("全局房间广播失败: roomId={}", roomId, e);
            return 0;
        }
    }

    @Override
    public int getGlobalOnlineUserCount() {
        try {
            // 统计所有节点的在线用户数
            Set<Object> onlineNodes = redisTemplate.opsForSet().members(ONLINE_NODES_KEY);
            int totalCount = 0;
            
            if (onlineNodes != null) {
                for (Object nodeObj : onlineNodes) {
                    String nodeId = nodeObj.toString();
                    String nodeUsersKey = NODE_USERS_KEY + nodeId;
                    Long nodeUserCount = redisTemplate.opsForSet().size(nodeUsersKey);
                    totalCount += (nodeUserCount != null ? nodeUserCount.intValue() : 0);
                }
            }
            
            return totalCount;
        } catch (Exception e) {
            log.error("获取全局在线用户数失败", e);
            return 0;
        }
    }

    @Override
    public String getCurrentNodeId() {
        return currentNodeId;
    }

    @Override
    public void handleMessageForward(Long userId, WebSocketMessage message) {
        try {
            // 处理来自其他节点的消息转发请求
            boolean sent = localConnectionManager.sendMessageToUser(userId, message);
            log.debug("处理消息转发: userId={}, sent={}", userId, sent);
        } catch (Exception e) {
            log.error("处理消息转发失败: userId={}, message={}", userId, message, e);
        }
    }

    @Override
    public void joinRoomGlobally(Long userId, String roomId) {
        try {
            // 1. 本地加入房间
            // 注意：这里需要通过Channel获取，实际实现中需要调整
            
            // 2. 全局同步房间成员信息
            String roomUsersKey = ROOM_USERS_GLOBAL_KEY + roomId;
            redisTemplate.opsForSet().add(roomUsersKey, userId.toString());
            redisTemplate.expire(roomUsersKey, 1, TimeUnit.DAYS);
            
            log.info("用户全局加入房间: userId={}, roomId={}", userId, roomId);
        } catch (Exception e) {
            log.error("用户全局加入房间失败: userId={}, roomId={}", userId, roomId, e);
        }
    }

    @Override
    public void leaveRoomGlobally(Long userId, String roomId) {
        try {
            // 1. 本地离开房间
            // 注意：这里需要通过Channel获取，实际实现中需要调整
            
            // 2. 全局同步房间成员信息
            String roomUsersKey = ROOM_USERS_GLOBAL_KEY + roomId;
            redisTemplate.opsForSet().remove(roomUsersKey, userId.toString());
            
            log.info("用户全局离开房间: userId={}, roomId={}", userId, roomId);
        } catch (Exception e) {
            log.error("用户全局离开房间失败: userId={}, roomId={}", userId, roomId, e);
        }
    }

    @Override
    public Set<Long> getRoomUsersGlobally(String roomId) {
        try {
            String roomUsersKey = ROOM_USERS_GLOBAL_KEY + roomId;
            Set<Object> userIds = redisTemplate.opsForSet().members(roomUsersKey);
            
            return userIds != null ? 
                userIds.stream()
                    .map(obj -> Long.parseLong(obj.toString()))
                    .collect(java.util.stream.Collectors.toSet()) :
                new java.util.HashSet<>();
        } catch (Exception e) {
            log.error("获取房间全局用户列表失败: roomId={}", roomId, e);
            return new java.util.HashSet<>();
        }
    }

    /**
     * 注册当前节点
     */
    private void registerCurrentNode() {
        try {
            // 注册节点到在线节点集合
            redisTemplate.opsForSet().add(ONLINE_NODES_KEY, currentNodeId);
            redisTemplate.expire(ONLINE_NODES_KEY, 1, TimeUnit.HOURS);

            // 设置节点心跳信息，TTL设置为60秒，给心跳发送留出足够缓冲时间
            String nodeHeartbeatKey = "chat:node:heartbeat:" + currentNodeId;
            redisTemplate.opsForValue().set(nodeHeartbeatKey, System.currentTimeMillis(), 60, TimeUnit.SECONDS);

            // 启动心跳任务
            startHeartbeatTask();

            log.info("当前节点注册成功: nodeId={}", currentNodeId);
        } catch (Exception e) {
            log.error("注册当前节点失败: nodeId={}", currentNodeId, e);
            throw new RuntimeException("节点注册失败", e);
        }
    }

    /**
     * 启动心跳任务
     */
    private void startHeartbeatTask() {
        scheduledExecutor = Executors.newSingleThreadScheduledExecutor(r -> {
            Thread t = new Thread(r, "chat-heartbeat-" + currentNodeId);
            t.setDaemon(true);
            return t;
        });

        // 每10秒发送一次心跳
        scheduledExecutor.scheduleAtFixedRate(this::sendHeartbeat, 10, 10, TimeUnit.SECONDS);

        // 每30秒清理一次离线节点
        scheduledExecutor.scheduleAtFixedRate(this::cleanupOfflineNodes, 30, 30, TimeUnit.SECONDS);
    }

    /**
     * 关闭定时任务
     */
    @PreDestroy
    public void shutdown() {
        try {
            if (scheduledExecutor != null && !scheduledExecutor.isShutdown()) {
                scheduledExecutor.shutdown();
                if (!scheduledExecutor.awaitTermination(5, TimeUnit.SECONDS)) {
                    scheduledExecutor.shutdownNow();
                }
            }

            // 清理当前节点信息
            removeOfflineNode(currentNodeId);

            log.info("分布式连接管理器已关闭: nodeId={}", currentNodeId);
        } catch (Exception e) {
            log.error("关闭分布式连接管理器失败", e);
        }
    }

    /**
     * 发送心跳
     */
    private void sendHeartbeat() {
        try {
            String nodeHeartbeatKey = "chat:node:heartbeat:" + currentNodeId;
            redisTemplate.opsForValue().set(nodeHeartbeatKey, System.currentTimeMillis(), 60, TimeUnit.SECONDS);

            // 续期节点注册
            redisTemplate.opsForSet().add(ONLINE_NODES_KEY, currentNodeId);
            redisTemplate.expire(ONLINE_NODES_KEY, 1, TimeUnit.HOURS);

        } catch (Exception e) {
            log.error("发送心跳失败: nodeId={}", currentNodeId, e);
        }
    }

    /**
     * 清理离线节点
     */
    public void cleanupOfflineNodes() {
        try {
            Set<Object> onlineNodes = redisTemplate.opsForSet().members(ONLINE_NODES_KEY);
            if (onlineNodes == null || onlineNodes.isEmpty()) {
                return;
            }

            long currentTime = System.currentTimeMillis();
            for (Object nodeObj : onlineNodes) {
                String nodeId = nodeObj.toString();
                String heartbeatKey = "chat:node:heartbeat:" + nodeId;

                Object heartbeatObj = redisTemplate.opsForValue().get(heartbeatKey);
                if (heartbeatObj == null) {
                    // 没有心跳信息，认为节点离线
                    removeOfflineNode(nodeId);
                    continue;
                }

                long lastHeartbeat = Long.parseLong(heartbeatObj.toString());
                // 心跳TTL是30秒，清理阈值应该更宽松，避免误删活跃节点
                if (currentTime - lastHeartbeat > 90000) { // 90秒没有心跳认为离线
                    log.warn("节点心跳超时，准备移除: nodeId={}, lastHeartbeat={}, currentTime={}, diff={}ms",
                        nodeId, lastHeartbeat, currentTime, currentTime - lastHeartbeat);
                    removeOfflineNode(nodeId);
                } else {
                    log.debug("节点心跳正常: nodeId={}, lastHeartbeat={}, diff={}ms",
                        nodeId, lastHeartbeat, currentTime - lastHeartbeat);
                }
            }
        } catch (Exception e) {
            log.error("清理离线节点失败", e);
        }
    }

    /**
     * 移除离线节点
     */
    private void removeOfflineNode(String nodeId) {
        try {
            // 从在线节点集合中移除
            redisTemplate.opsForSet().remove(ONLINE_NODES_KEY, nodeId);

            // 清理节点的用户连接信息
            String nodeUsersKey = NODE_USERS_KEY + nodeId;
            Set<Object> userIds = redisTemplate.opsForSet().members(nodeUsersKey);

            if (userIds != null && !userIds.isEmpty()) {
                for (Object userIdObj : userIds) {
                    String userId = userIdObj.toString();
                    String userNodeKey = USER_NODE_KEY + userId;
                    redisTemplate.delete(userNodeKey);
                }
            }

            // 删除节点用户列表
            redisTemplate.delete(nodeUsersKey);

            // 删除心跳信息
            String heartbeatKey = "chat:node:heartbeat:" + nodeId;
            redisTemplate.delete(heartbeatKey);

            log.info("离线节点已清理: nodeId={}", nodeId);
        } catch (Exception e) {
            log.error("清理离线节点失败: nodeId={}", nodeId, e);
        }
    }

    /**
     * 发送用户状态事件
     */
    private void sendUserStatusEvent(Long userId, String status, String nodeId) {
        try {
            log.info("创建用户状态事件: userId={}, status={}, nodeId={}", userId, status, nodeId);
            UserStatusChangedEvent event = new UserStatusChangedEvent(
                serviceName,
                String.valueOf(userId),
                userId,
                status,
                nodeId
            );

            log.info("发送用户状态事件到RocketMQ: userId={}, status={}, eventId={}", userId, status, event.getEventId());
            messageProducer.sendMessageAsync(
                MessageConstants.TOPIC_NOTIFICATION,
                MessageConstants.TAG_USER_STATUS_CHANGED,
                event,
                null
            );
            log.info("用户状态事件发送成功: userId={}, status={}, eventId={}", userId, status, event.getEventId());
        } catch (Exception e) {
            log.error("发送用户状态事件失败: userId={}, status={}, nodeId={}", userId, status, nodeId, e);
            // 不要重新抛出异常，避免影响连接建立
        }
    }

    /**
     * 发送消息转发请求
     */
    private void sendMessageForwardRequest(String targetNodeId, Long userId, WebSocketMessage message) {
        try {
            MessageForwardEvent event = new MessageForwardEvent(serviceName, String.valueOf(userId));
            event.setType("MESSAGE_FORWARD");
            event.setTargetNodeId(targetNodeId);
            event.setTargetUserId(userId);
            event.setMessage(message);
            event.setFromNodeId(currentNodeId);
            
            messageProducer.sendMessageAsync(
                MessageConstants.TOPIC_NOTIFICATION,
                "MESSAGE_FORWARD",
                event,
                null
            );
        } catch (Exception e) {
            log.error("发送消息转发请求失败: targetNodeId={}, userId={}", targetNodeId, userId, e);
        }
    }

    /**
     * 发送房间广播请求
     */
    private void sendRoomBroadcastRequest(String roomId, WebSocketMessage message, Long excludeUserId) {
        try {
            MessageForwardEvent event = new MessageForwardEvent(serviceName, null);
            event.setType("ROOM_BROADCAST");
            event.setRoomId(roomId);
            event.setMessage(message);
            event.setExcludeUserId(excludeUserId);
            event.setFromNodeId(currentNodeId);
            
            messageProducer.sendMessageAsync(
                MessageConstants.TOPIC_NOTIFICATION,
                "ROOM_BROADCAST",
                event,
                null
            );
        } catch (Exception e) {
            log.error("发送房间广播请求失败: roomId={}", roomId, e);
        }
    }
}
