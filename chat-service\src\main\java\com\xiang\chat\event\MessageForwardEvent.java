package com.xiang.chat.event;

import com.xiang.chat.dto.WebSocketMessage;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 消息转发事件
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class MessageForwardEvent extends BaseEvent {

    private static final long serialVersionUID = 1L;

    /**
     * 转发类型
     */
    private String type;

    /**
     * 目标节点ID
     */
    private String targetNodeId;

    /**
     * 目标用户ID
     */
    private Long targetUserId;

    /**
     * 消息内容
     */
    private WebSocketMessage message;

    /**
     * 来源节点ID
     */
    private String fromNodeId;

    /**
     * 房间ID (用于房间广播)
     */
    private String roomId;

    /**
     * 排除用户ID (用于房间广播)
     */
    private Long excludeUserId;

    public MessageForwardEvent() {
        super();
    }

    public MessageForwardEvent(String source, String userId) {
        super(source, userId);
    }
}