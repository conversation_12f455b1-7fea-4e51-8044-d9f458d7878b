package com.xiang.chat.service;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestTemplate;

import java.util.Base64;
import java.util.Map;

@Slf4j
@Service
public class OAuth2AuthService {

    @Value("${oauth2.auth-server.token-uri}")
    private String tokenUri;

    @Value("${oauth2.auth-server.client-id}")
    private String clientId;

    @Value("${oauth2.auth-server.client-secret}")
    private String clientSecret;

    private final RestTemplate restTemplate = new RestTemplate();

    /**
     * 通过用户名密码获取OAuth2 Token
     */
    public OAuth2TokenResponse authenticate(String username, String password) {
        try {
            // 构建请求头
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);
            
            // 添加Basic认证头
            String credentials = clientId + ":" + clientSecret;
            String encodedCredentials = Base64.getEncoder().encodeToString(credentials.getBytes());
            headers.set("Authorization", "Basic " + encodedCredentials);

            // 构建请求体
            MultiValueMap<String, String> body = new LinkedMultiValueMap<>();
            body.add("grant_type", "password");
            body.add("username", username);
            body.add("password", password);
            body.add("scope", "read write");

            HttpEntity<MultiValueMap<String, String>> request = new HttpEntity<>(body, headers);

            // 发送请求
            ResponseEntity<Map> response = restTemplate.postForEntity(tokenUri, request, Map.class);

            if (response.getStatusCode() == HttpStatus.OK && response.getBody() != null) {
                Map<String, Object> responseBody = response.getBody();
                
                return OAuth2TokenResponse.builder()
                        .accessToken((String) responseBody.get("access_token"))
                        .tokenType((String) responseBody.get("token_type"))
                        .expiresIn((Integer) responseBody.get("expires_in"))
                        .refreshToken((String) responseBody.get("refresh_token"))
                        .scope((String) responseBody.get("scope"))
                        .success(true)
                        .build();
            } else {
                log.error("OAuth2 authentication failed: {}", response.getStatusCode());
                return OAuth2TokenResponse.builder()
                        .success(false)
                        .error("authentication_failed")
                        .errorDescription("Failed to authenticate with auth server")
                        .build();
            }

        } catch (org.springframework.web.client.HttpClientErrorException.Unauthorized e) {
            log.warn("OAuth2 authentication failed - Unauthorized: username={}", username);
            return OAuth2TokenResponse.builder()
                    .success(false)
                    .error("invalid_credentials")
                    .errorDescription("用户名或密码错误")
                    .build();
        } catch (org.springframework.web.client.HttpClientErrorException.BadRequest e) {
            log.warn("OAuth2 authentication failed - Bad Request: username={}", username);
            return OAuth2TokenResponse.builder()
                    .success(false)
                    .error("invalid_request")
                    .errorDescription("请求参数错误")
                    .build();
        } catch (org.springframework.web.client.ResourceAccessException e) {
            log.error("OAuth2 authentication failed - Cannot connect to auth server: {}", e.getMessage());
            return OAuth2TokenResponse.builder()
                    .success(false)
                    .error("server_unavailable")
                    .errorDescription("认证服务器不可用，请稍后重试")
                    .build();
        } catch (Exception e) {
            log.error("OAuth2 authentication error for user: {}", username, e);
            return OAuth2TokenResponse.builder()
                    .success(false)
                    .error("authentication_error")
                    .errorDescription("认证过程中发生错误: " + e.getMessage())
                    .build();
        }
    }

    /**
     * 验证Token是否有效
     */
    public boolean validateToken(String token) {
        try {
            // 这里可以调用auth-server的token验证端点
            // 简化实现，实际应该调用 /oauth2/introspect 端点
            return token != null && !token.isEmpty();
        } catch (Exception e) {
            log.error("Token validation error", e);
            return false;
        }
    }
}