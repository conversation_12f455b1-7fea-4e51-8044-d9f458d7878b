-- 创建聊天服务数据库
CREATE DATABASE IF NOT EXISTS springcloud_chat DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

USE springcloud_chat;

-- 聊天消息表
CREATE TABLE IF NOT EXISTS chat_message (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    message_id VARCHAR(64) NOT NULL UNIQUE COMMENT '消息ID（唯一标识）',
    sender_id BIGINT NOT NULL COMMENT '发送者ID',
    receiver_id BIGINT NULL COMMENT '接收者ID（私聊时使用）',
    room_id VARCHAR(64) NULL COMMENT '房间ID（群聊时使用）',
    message_type VARCHAR(20) NOT NULL DEFAULT 'text' COMMENT '消息类型：text,image,file,voice,video,system',
    content TEXT NOT NULL COMMENT '消息内容',
    extra_data JSON NULL COMMENT '消息扩展数据（JSON格式）',
    send_time DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '发送时间',
    status TINYINT NOT NULL DEFAULT 1 COMMENT '消息状态：0-发送中,1-已发送,2-已送达,3-已读,-1-发送失败',
    create_time DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '创建时间',
    update_time DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3) COMMENT '更新时间',
    deleted TINYINT NOT NULL DEFAULT 0 COMMENT '逻辑删除标志：0-未删除,1-已删除',

    -- 复合索引优化查询性能
    INDEX idx_room_send_time (room_id, send_time DESC, deleted),
    INDEX idx_private_chat (sender_id, receiver_id, send_time DESC, deleted),
    INDEX idx_sender_time (sender_id, send_time DESC),
    INDEX idx_receiver_time (receiver_id, send_time DESC),
    INDEX idx_message_type_time (message_type, send_time DESC),
    INDEX idx_status_time (status, send_time DESC),

    -- 约束检查
    CONSTRAINT chk_message_target CHECK (
        (room_id IS NOT NULL AND receiver_id IS NULL) OR
        (room_id IS NULL AND receiver_id IS NOT NULL)
    ),
    CONSTRAINT chk_message_type CHECK (message_type IN ('text', 'image', 'file', 'voice', 'video', 'system')),
    CONSTRAINT chk_status CHECK (status IN (-1, 0, 1, 2, 3)),
    CONSTRAINT chk_deleted CHECK (deleted IN (0, 1))
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='聊天消息表';

-- 聊天房间表
CREATE TABLE IF NOT EXISTS chat_room (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    room_id VARCHAR(64) NOT NULL UNIQUE COMMENT '房间ID（唯一标识）',
    room_name VARCHAR(100) NOT NULL COMMENT '房间名称',
    description VARCHAR(500) NULL COMMENT '房间描述',
    room_type VARCHAR(20) NOT NULL DEFAULT 'public' COMMENT '房间类型：public-公开,private-私有,group-群组',
    creator_id BIGINT NOT NULL COMMENT '房间创建者ID',
    max_users INT NOT NULL DEFAULT 100 COMMENT '最大用户数',
    current_users INT NOT NULL DEFAULT 0 COMMENT '当前用户数',
    status TINYINT NOT NULL DEFAULT 1 COMMENT '房间状态：0-禁用,1-启用',
    config JSON NULL COMMENT '房间配置（JSON格式）',
    create_time DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '创建时间',
    update_time DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3) COMMENT '更新时间',
    deleted TINYINT NOT NULL DEFAULT 0 COMMENT '逻辑删除标志：0-未删除,1-已删除',

    -- 复合索引优化
    INDEX idx_type_status_time (room_type, status, create_time DESC),
    INDEX idx_creator_time (creator_id, create_time DESC),
    INDEX idx_status_users (status, current_users DESC),
    FULLTEXT idx_search (room_name, description),

    -- 约束检查
    CONSTRAINT chk_room_type CHECK (room_type IN ('public', 'private', 'group')),
    CONSTRAINT chk_room_status CHECK (status IN (0, 1)),
    CONSTRAINT chk_room_deleted CHECK (deleted IN (0, 1)),
    CONSTRAINT chk_max_users CHECK (max_users > 0 AND max_users <= 10000),
    CONSTRAINT chk_current_users CHECK (current_users >= 0 AND current_users <= max_users)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='聊天房间表';

-- 房间成员表
CREATE TABLE IF NOT EXISTS chat_room_member (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    room_id VARCHAR(64) NOT NULL COMMENT '房间ID',
    user_id BIGINT NOT NULL COMMENT '用户ID',
    role VARCHAR(20) NOT NULL DEFAULT 'member' COMMENT '角色：owner-房主,admin-管理员,member-普通成员',
    join_time DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '加入时间',
    last_read_time DATETIME(3) NULL COMMENT '最后阅读时间',
    mute_until DATETIME(3) NULL COMMENT '禁言到期时间',
    status TINYINT NOT NULL DEFAULT 1 COMMENT '状态：0-已退出,1-正常',
    create_time DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '创建时间',
    update_time DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3) COMMENT '更新时间',

    UNIQUE KEY uk_room_user (room_id, user_id),
    INDEX idx_room_status_join (room_id, status, join_time DESC),
    INDEX idx_user_status_join (user_id, status, join_time DESC),
    INDEX idx_role_room (role, room_id),
    INDEX idx_mute_until (mute_until),

    -- 外键约束
    FOREIGN KEY fk_member_room (room_id) REFERENCES chat_room(room_id) ON DELETE CASCADE,

    -- 约束检查
    CONSTRAINT chk_member_role CHECK (role IN ('owner', 'admin', 'member')),
    CONSTRAINT chk_member_status CHECK (status IN (0, 1))
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='房间成员表';

-- 用户好友表
CREATE TABLE IF NOT EXISTS chat_friend (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    user_id BIGINT NOT NULL COMMENT '用户ID',
    friend_id BIGINT NOT NULL COMMENT '好友ID',
    nickname VARCHAR(50) NULL COMMENT '好友备注名',
    status TINYINT NOT NULL DEFAULT 1 COMMENT '状态：0-已删除,1-正常,2-已拉黑',
    create_time DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '创建时间',
    update_time DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3) COMMENT '更新时间',

    UNIQUE KEY uk_user_friend (user_id, friend_id),
    INDEX idx_user_status_time (user_id, status, create_time DESC),
    INDEX idx_friend_status (friend_id, status),

    -- 约束检查
    CONSTRAINT chk_friend_status CHECK (status IN (0, 1, 2)),
    CONSTRAINT chk_not_self_friend CHECK (user_id != friend_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户好友表';

-- 消息已读状态表
CREATE TABLE IF NOT EXISTS chat_message_read (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    message_id VARCHAR(64) NOT NULL COMMENT '消息ID',
    user_id BIGINT NOT NULL COMMENT '用户ID',
    read_time DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '阅读时间',

    UNIQUE KEY uk_message_user (message_id, user_id),
    INDEX idx_message_read_time (message_id, read_time DESC),
    INDEX idx_user_read_time (user_id, read_time DESC),

    -- 外键约束
    FOREIGN KEY fk_read_message (message_id) REFERENCES chat_message(message_id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='消息已读状态表';

-- 文件信息表
CREATE TABLE IF NOT EXISTS chat_file (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    file_id VARCHAR(64) NOT NULL UNIQUE COMMENT '文件ID',
    original_name VARCHAR(255) NOT NULL COMMENT '原始文件名',
    file_name VARCHAR(255) NOT NULL COMMENT '存储文件名',
    file_path VARCHAR(500) NOT NULL COMMENT '文件路径',
    file_size BIGINT NOT NULL COMMENT '文件大小（字节）',
    file_type VARCHAR(50) NOT NULL COMMENT '文件类型',
    mime_type VARCHAR(100) NOT NULL COMMENT 'MIME类型',
    uploader_id BIGINT NOT NULL COMMENT '上传者ID',
    upload_time DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '上传时间',
    download_count INT NOT NULL DEFAULT 0 COMMENT '下载次数',
    status TINYINT NOT NULL DEFAULT 1 COMMENT '状态：0-已删除,1-正常',

    INDEX idx_uploader_time (uploader_id, upload_time DESC),
    INDEX idx_type_status_time (file_type, status, upload_time DESC),
    INDEX idx_status_size (status, file_size DESC),

    -- 约束检查
    CONSTRAINT chk_file_status CHECK (status IN (0, 1)),
    CONSTRAINT chk_file_size CHECK (file_size > 0 AND file_size <= 104857600), -- 最大100MB
    CONSTRAINT chk_download_count CHECK (download_count >= 0)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='文件信息表';

-- 创建触发器：自动更新房间当前用户数
DELIMITER $$

CREATE TRIGGER tr_room_member_insert
AFTER INSERT ON chat_room_member
FOR EACH ROW
BEGIN
    IF NEW.status = 1 THEN
        UPDATE chat_room
        SET current_users = current_users + 1
        WHERE room_id = NEW.room_id;
    END IF;
END$$

CREATE TRIGGER tr_room_member_update
AFTER UPDATE ON chat_room_member
FOR EACH ROW
BEGIN
    IF OLD.status != NEW.status THEN
        IF NEW.status = 1 AND OLD.status = 0 THEN
            -- 用户重新加入
            UPDATE chat_room
            SET current_users = current_users + 1
            WHERE room_id = NEW.room_id;
        ELSEIF NEW.status = 0 AND OLD.status = 1 THEN
            -- 用户退出
            UPDATE chat_room
            SET current_users = current_users - 1
            WHERE room_id = NEW.room_id;
        END IF;
    END IF;
END$$

CREATE TRIGGER tr_room_member_delete
AFTER DELETE ON chat_room_member
FOR EACH ROW
BEGIN
    IF OLD.status = 1 THEN
        UPDATE chat_room
        SET current_users = current_users - 1
        WHERE room_id = OLD.room_id;
    END IF;
END$$

DELIMITER ;

-- 插入默认数据
INSERT INTO chat_room (room_id, room_name, description, room_type, creator_id, max_users, config) VALUES
('public_general', '大厅', '公共聊天大厅，欢迎大家交流', 'public', 1, 500, '{"allowAnonymous": true, "messageRetentionDays": 30}'),
('public_tech', '技术交流', '技术相关话题讨论', 'public', 1, 200, '{"allowAnonymous": false, "messageRetentionDays": 90}'),
('public_random', '随便聊聊', '随意聊天的地方', 'public', 1, 100, '{"allowAnonymous": true, "messageRetentionDays": 7}');

-- 插入房间创建者为成员
INSERT INTO chat_room_member (room_id, user_id, role, status) VALUES
('public_general', 1, 'owner', 1),
('public_tech', 1, 'owner', 1),
('public_random', 1, 'owner', 1);

-- 插入测试消息
INSERT INTO chat_message (message_id, sender_id, room_id, message_type, content, send_time) VALUES
('msg_001', 1, 'public_general', 'text', '欢迎来到聊天室！', NOW()),
('msg_002', 1, 'public_tech', 'text', '这里是技术交流区，欢迎讨论技术问题', NOW()),
('msg_003', 1, 'public_random', 'text', '随便聊聊，放松一下', NOW());
