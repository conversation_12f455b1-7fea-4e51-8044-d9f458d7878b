package com.xiang.chat.messaging;

import com.alibaba.fastjson2.JSON;
import com.xiang.chat.constants.MessageConstants;
import com.xiang.chat.event.ChatMessageEvent;
import com.xiang.chat.service.ConnectionManager;
import com.xiang.chat.service.DistributedConnectionManager;
import com.xiang.chat.service.DistributedOfflineNotificationService;
import com.xiang.chat.service.OfflineMessageService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.spring.annotation.ConsumeMode;
import org.apache.rocketmq.spring.annotation.MessageModel;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.apache.rocketmq.spring.core.RocketMQListener;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;

import java.util.concurrent.TimeUnit;

/**
 * 聊天事件消费者
 */
@Slf4j
@Component
@RequiredArgsConstructor
@RocketMQMessageListener(
    topic = MessageConstants.TOPIC_NOTIFICATION,
    consumerGroup = "chat-service-group",
    consumeMode = ConsumeMode.CONCURRENTLY,
    messageModel = MessageModel.CLUSTERING,
    selectorExpression = "CHAT_MESSAGE"
)
public class ChatEventConsumer implements RocketMQListener<String> {

    private final ConnectionManager connectionManager;
    private final DistributedConnectionManager distributedConnectionManager;
    private final DistributedOfflineNotificationService offlineNotificationService;
    private final OfflineMessageService offlineMessageService;
    private final RedisTemplate<String, Object> redisTemplate;

    @Override
    public void onMessage(String messageBody) {
        try {
            log.info("接收到聊天消息事件: {}", messageBody);

            ChatMessageEvent event = parseMessage(messageBody);
            if (event != null) {
                handleMessage(event);
            }
        } catch (Exception e) {
            log.error("处理聊天消息事件失败", e);
        }
    }

    private ChatMessageEvent parseMessage(String messageBody) {
        try {
            return JSON.parseObject(messageBody, ChatMessageEvent.class);
        } catch (Exception e) {
            log.error("解析聊天消息事件失败: {}", messageBody, e);
            return null;
        }
    }

    private void handleMessage(ChatMessageEvent event) throws Exception {
        log.info("处理聊天消息事件: messageId={}, senderId={}, messageType={}", 
            event.getMessageId(), event.getSenderId(), event.getMessageType());
        
        // 根据事件类型进行不同的处理
        switch (event.getEventType()) {
            case "ChatMessageEvent":
            case "CHAT_MESSAGE":
                handleChatMessageEvent(event);
                break;
            default:
                log.warn("未知的聊天事件类型: {}", event.getEventType());
        }
    }

    /**
     * 处理聊天消息事件
     */
    private void handleChatMessageEvent(ChatMessageEvent event) {
        try {
            // 1. 更新消息统计
            updateMessageStatistics(event);
            
            // 2. 处理消息推送通知
            handleMessageNotification(event);
            
            // 3. 更新用户活跃度
            updateUserActivity(event);
            
            // 4. 内容审核（如果需要）
            if (needContentModeration(event)) {
                moderateContent(event);
            }
            
            log.info("聊天消息事件处理完成: messageId={}", event.getMessageId());
            
        } catch (Exception e) {
            log.error("处理聊天消息事件失败: messageId={}", event.getMessageId(), e);
            throw e;
        }
    }

    /**
     * 更新消息统计
     */
    private void updateMessageStatistics(ChatMessageEvent event) {
        try {
            String dateKey = java.time.LocalDate.now().toString();
            
            // 全局消息统计
            String globalKey = "chat:stats:global:" + dateKey;
            redisTemplate.opsForHash().increment(globalKey, "totalMessages", 1);
            redisTemplate.opsForHash().increment(globalKey, "totalChars", event.getContentLength());
            redisTemplate.expire(globalKey, 30, TimeUnit.DAYS);
            
            // 用户消息统计
            String userKey = "chat:stats:user:" + event.getSenderId() + ":" + dateKey;
            redisTemplate.opsForHash().increment(userKey, "messageCount", 1);
            redisTemplate.opsForHash().increment(userKey, "charCount", event.getContentLength());
            redisTemplate.expire(userKey, 30, TimeUnit.DAYS);
            
            // 房间消息统计（如果是群聊）
            if (event.getRoomId() != null) {
                String roomKey = "chat:stats:room:" + event.getRoomId() + ":" + dateKey;
                redisTemplate.opsForHash().increment(roomKey, "messageCount", 1);
                redisTemplate.expire(roomKey, 30, TimeUnit.DAYS);
            }
            
            // 消息类型统计
            String typeKey = "chat:stats:type:" + event.getMessageType() + ":" + dateKey;
            redisTemplate.opsForHash().increment(typeKey, "count", 1);
            redisTemplate.expire(typeKey, 30, TimeUnit.DAYS);
            
        } catch (Exception e) {
            log.error("更新消息统计失败: messageId={}", event.getMessageId(), e);
        }
    }

    /**
     * 处理消息推送通知
     */
    private void handleMessageNotification(ChatMessageEvent event) {
        try {
            // 如果是私聊消息，使用分布式离线通知服务处理
            if (event.getIsPrivate() && event.getReceiverId() != null) {
                offlineNotificationService.handlePrivateMessageOfflineNotification(event);
            }

            // 如果是群聊消息，检查@提及的用户
            if (event.getRoomId() != null && event.getContent().contains("@")) {
                handleMentionNotifications(event);
            }

        } catch (Exception e) {
            log.error("处理消息推送通知失败: messageId={}", event.getMessageId(), e);
        }
    }

    /**
     * 更新用户活跃度
     */
    private void updateUserActivity(ChatMessageEvent event) {
        try {
            String key = "chat:activity:user:" + event.getSenderId();
            redisTemplate.opsForValue().set(key, System.currentTimeMillis(), 1, TimeUnit.HOURS);
            
            // 更新用户最后发言时间
            String lastMessageKey = "chat:user:lastMessage:" + event.getSenderId();
            redisTemplate.opsForValue().set(lastMessageKey, event.getEventTime().toString(), 7, TimeUnit.DAYS);
            
        } catch (Exception e) {
            log.error("更新用户活跃度失败: userId={}", event.getSenderId(), e);
        }
    }

    /**
     * 检查是否需要内容审核
     */
    private boolean needContentModeration(ChatMessageEvent event) {
        // 简单的内容审核规则
        if (event.getContent() == null) {
            return false;
        }
        
        String content = event.getContent().toLowerCase();
        
        // 检查敏感词（这里只是示例，实际应该使用专业的内容审核服务）
        String[] sensitiveWords = {"spam", "广告", "违法", "欺诈"};
        for (String word : sensitiveWords) {
            if (content.contains(word)) {
                return true;
            }
        }
        
        // 检查消息长度
        if (event.getContentLength() > 1000) {
            return true;
        }
        
        return false;
    }

    /**
     * 内容审核
     */
    private void moderateContent(ChatMessageEvent event) {
        try {
            log.warn("检测到需要审核的内容: messageId={}, senderId={}, content={}", 
                event.getMessageId(), event.getSenderId(), event.getContent());
            
            // 记录审核日志
            String auditKey = "chat:audit:" + event.getMessageId();
            redisTemplate.opsForHash().put(auditKey, "messageId", event.getMessageId());
            redisTemplate.opsForHash().put(auditKey, "senderId", event.getSenderId());
            redisTemplate.opsForHash().put(auditKey, "content", event.getContent());
            redisTemplate.opsForHash().put(auditKey, "auditTime", System.currentTimeMillis());
            redisTemplate.opsForHash().put(auditKey, "status", "PENDING");
            redisTemplate.expire(auditKey, 30, TimeUnit.DAYS);
            
            // 这里可以调用第三方内容审核API
            // 或者将消息加入人工审核队列
            
        } catch (Exception e) {
            log.error("内容审核失败: messageId={}", event.getMessageId(), e);
        }
    }



    /**
     * 处理@提及通知
     */
    private void handleMentionNotifications(ChatMessageEvent event) {
        try {
            // 简单的@提及解析（实际应该使用更复杂的解析逻辑）
            String content = event.getContent();
            if (content.contains("@all")) {
                // @所有人
                notifyAllRoomMembers(event);
            } else {
                // 解析具体的@用户
                // 这里简化处理，实际应该解析@username格式
                log.info("检测到@提及: messageId={}, content={}", event.getMessageId(), content);
            }
            
        } catch (Exception e) {
            log.error("处理@提及通知失败: messageId={}", event.getMessageId(), e);
        }
    }

    /**
     * 通知房间所有成员
     */
    private void notifyAllRoomMembers(ChatMessageEvent event) {
        try {
            // 获取房间所有成员
            java.util.Set<Long> roomMembers = connectionManager.getRoomUsers(event.getRoomId());

            int onlineCount = 0;
            int offlineCount = 0;

            for (Long memberId : roomMembers) {
                if (!memberId.equals(event.getSenderId())) {
                    // 使用分布式离线通知服务处理@all通知
                    boolean isOnlineGlobally = distributedConnectionManager.isUserOnlineGlobally(memberId);

                    if (isOnlineGlobally) {
                        onlineCount++;
                        log.debug("@all通知：用户在线，跳过推送: userId={}, messageId={}",
                            memberId, event.getMessageId());
                    } else {
                        offlineCount++;
                        // 使用分布式离线通知服务处理@all推送
                        offlineNotificationService.handleMentionOfflineNotification(event, memberId);
                    }
                }
            }

            log.info("@all通知处理完成: messageId={}, 在线用户数={}, 离线用户数={}",
                event.getMessageId(), onlineCount, offlineCount);

        } catch (Exception e) {
            log.error("通知房间所有成员失败: messageId={}", event.getMessageId(), e);
        }
    }


}
