package com.xiang.chat;

import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.openfeign.EnableFeignClients;

/**
 * 聊天服务启动类
 */
@MapperScan("com.xiang.chat.mapper")
@SpringBootApplication(scanBasePackages = {
        "com.xiang.chat",
        "com.xiang.springcloudmybaits"
})
@EnableFeignClients(basePackages = "com.xiang.chat")
public class ChatServiceApplication {

    public static void main(String[] args) {
        SpringApplication.run(ChatServiceApplication.class, args);
    }
}
