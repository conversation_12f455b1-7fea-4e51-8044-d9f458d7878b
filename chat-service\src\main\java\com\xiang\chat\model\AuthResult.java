package com.xiang.chat.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 认证结果
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AuthResult {
    
    /**
     * 认证是否成功
     */
    private boolean success;
    
    /**
     * 用户ID
     */
    private Long userId;
    
    /**
     * 用户名
     */
    private String username;
    
    /**
     * 昵称
     */
    private String nickname;
    
    /**
     * 头像
     */
    private String avatar;
    
    /**
     * 错误消息
     */
    private String errorMessage;
    
    /**
     * 错误代码
     */
    private String errorCode;
    
    /**
     * 令牌过期时间
     */
    private Long expiresAt;
    
    /**
     * 用户角色
     */
    private String role;
    
    /**
     * 创建成功结果
     */
    public static AuthResult success(Long userId, String username, String nickname, String avatar) {
        return AuthResult.builder()
                .success(true)
                .userId(userId)
                .username(username)
                .nickname(nickname)
                .avatar(avatar)
                .build();
    }
    
    /**
     * 创建成功结果（完整信息）
     */
    public static AuthResult success(Long userId, String username, String nickname, 
                                   String avatar, Long expiresAt, String role) {
        return AuthResult.builder()
                .success(true)
                .userId(userId)
                .username(username)
                .nickname(nickname)
                .avatar(avatar)
                .expiresAt(expiresAt)
                .role(role)
                .build();
    }
    
    /**
     * 创建失败结果
     */
    public static AuthResult failure(String errorMessage, String errorCode) {
        return AuthResult.builder()
                .success(false)
                .errorMessage(errorMessage)
                .errorCode(errorCode)
                .build();
    }
    
    /**
     * 令牌无效
     */
    public static AuthResult invalidToken() {
        return failure("无效的认证令牌", "INVALID_TOKEN");
    }
    
    /**
     * 令牌过期
     */
    public static AuthResult expiredToken() {
        return failure("认证令牌已过期", "EXPIRED_TOKEN");
    }
    
    /**
     * 用户不存在
     */
    public static AuthResult userNotFound() {
        return failure("用户不存在", "USER_NOT_FOUND");
    }
    
    /**
     * 用户被禁用
     */
    public static AuthResult userDisabled() {
        return failure("用户账号已被禁用", "USER_DISABLED");
    }
}
