# OAuth2 密码模式认证使用说明

## 🏗️ 架构说明

- **auth-service (端口9000)**: OAuth2授权服务器，提供密码模式认证
- **chat-service (端口8083)**: 聊天应用，通过auth-service进行用户认证

## 🚀 启动步骤

### 1. 启动基础设施
```bash
docker-compose up -d
```

### 2. 启动auth-service
```bash
cd auth-service
mvn spring-boot:run
```

### 3. 启动chat-service
```bash
cd chat-service
mvn spring-boot:run
```

## 🔐 认证流程

### 用户登录流程
1. 用户访问 http://localhost:8083
2. 跳转到登录页面 http://localhost:8083/login
3. 输入用户名密码（admin/admin123 或 test/test123）
4. chat-service通过OAuth2密码模式向auth-service验证用户
5. 认证成功后跳转到聊天页面

### 技术实现
1. **用户提交登录表单** → chat-service SecurityConfig
2. **OAuth2AuthenticationProvider** → 调用OAuth2AuthService
3. **OAuth2AuthService** → 向auth-service发送密码模式请求
4. **auth-service** → 验证用户并返回JWT token
5. **chat-service** → 创建认证成功的Authentication对象

## 🧪 测试API

### 1. 测试auth-service健康状态
```bash
curl http://localhost:9000/test/health
```

### 2. 直接测试OAuth2密码模式
```bash
curl -X POST http://localhost:9000/oauth2/token \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -H "Authorization: Basic Y2hhdC1jbGllbnQ6Y2hhdC1zZWNyZXQ=" \
  -d "grant_type=password&username=admin&password=admin123&scope=read write"
```

### 3. 测试chat-service认证
```bash
curl -X POST http://localhost:8083/test/auth \
  -H "Content-Type: application/json" \
  -d '{"username":"admin","password":"admin123"}'
```

### 4. 测试chat-service健康状态
```bash
curl http://localhost:8083/test/health
```

## 📁 关键文件

### auth-service
- `AuthorizationServerConfig.java` - OAuth2服务器配置
- `SecurityConfig.java` - 安全配置，处理密码模式
- `PasswordGrantAuthenticationProvider.java` - 密码模式认证提供者
- `OAuth2Config.java` - OAuth2相关Bean配置

### chat-service
- `OAuth2AuthService.java` - OAuth2客户端服务
- `OAuth2AuthenticationProvider.java` - 自定义认证提供者
- `SecurityConfig.java` - 安全配置，使用OAuth2认证
- `LoginController.java` - 登录和聊天页面控制器

## 🔧 配置说明

### auth-service配置 (configs/auth-service-dev.yml)
```yaml
oauth2:
  jwt:
    access-token-validity: 3600
    refresh-token-validity: 86400
  client:
    client-id: chat-client
    client-secret: chat-secret
```

### chat-service配置 (configs/chat-service-dev.yml)
```yaml
oauth2:
  auth-server:
    token-uri: http://localhost:9000/oauth2/token
    client-id: chat-client
    client-secret: chat-secret
```

## 🎯 特性

- ✅ OAuth2密码模式认证
- ✅ JWT Token支持
- ✅ 数据库用户验证
- ✅ 自动token刷新支持
- ✅ 统一认证中心
- ✅ 微服务架构

## 🔍 故障排除

### 1. 认证失败
- 检查用户名密码是否正确
- 确认auth-service正常运行
- 查看auth-service日志

### 2. 连接失败
- 确认端口9000和8083未被占用
- 检查网络连接
- 验证配置文件中的URL

### 3. Token问题
- 检查客户端ID和密钥配置
- 确认token未过期
- 查看OAuth2相关日志