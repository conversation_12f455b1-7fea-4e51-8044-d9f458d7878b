package com.xiang.chat.messaging;

import io.micrometer.core.instrument.Counter;
import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.Timer;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableScheduling;

import java.util.concurrent.ConcurrentHashMap;

/**
 * RocketMQ监控指标配置
 */
@Slf4j
@Configuration
@EnableScheduling
@RequiredArgsConstructor
@ConditionalOnClass(MeterRegistry.class)
public class RocketMQMetricsConfiguration {

    private final MeterRegistry meterRegistry;
    private final ConcurrentHashMap<String, Timer> messageTimers = new ConcurrentHashMap<>();

    /**
     * 记录消息发送指标
     */
    public void recordMessageSent(String topic, String tag, String eventType, boolean success, long durationMs) {
        // 记录发送计数
        Counter.builder("rocketmq_message_sent_total")
                .description("Total number of messages sent")
                .tag("topic", topic)
                .tag("tag", tag)
                .tag("event_type", eventType)
                .tag("success", String.valueOf(success))
                .register(meterRegistry)
                .increment();

        // 记录发送耗时
        String timerKey = "send_" + topic + "_" + tag;
        Timer timer = messageTimers.computeIfAbsent(timerKey, k -> 
            Timer.builder("rocketmq_message_send_duration")
                    .description("Message send duration")
                    .tag("topic", topic)
                    .tag("tag", tag)
                    .register(meterRegistry)
        );
        timer.record(durationMs, java.util.concurrent.TimeUnit.MILLISECONDS);
    }

    /**
     * 记录消息消费指标
     */
    public void recordMessageConsumed(String topic, String tag, String eventType, boolean success, long durationMs) {
        // 记录消费计数
        Counter.builder("rocketmq_message_consumed_total")
                .description("Total number of messages consumed")
                .tag("topic", topic)
                .tag("tag", tag)
                .tag("event_type", eventType)
                .tag("success", String.valueOf(success))
                .register(meterRegistry)
                .increment();

        // 记录消费耗时
        String timerKey = "consume_" + topic + "_" + tag;
        Timer timer = messageTimers.computeIfAbsent(timerKey, k -> 
            Timer.builder("rocketmq_message_consume_duration")
                    .description("Message consume duration")
                    .tag("topic", topic)
                    .tag("tag", tag)
                    .register(meterRegistry)
        );
        timer.record(durationMs, java.util.concurrent.TimeUnit.MILLISECONDS);
    }

    /**
     * 记录消息发送失败
     */
    public void recordMessageSendFailure(String topic, String tag, String eventType, String errorType) {
        Counter.builder("rocketmq_message_send_failures_total")
                .description("Total number of message send failures")
                .tag("topic", topic)
                .tag("tag", tag)
                .tag("event_type", eventType)
                .tag("error_type", errorType)
                .register(meterRegistry)
                .increment();
    }

    /**
     * 记录消息消费失败
     */
    public void recordMessageConsumeFailure(String topic, String tag, String eventType, String errorType) {
        Counter.builder("rocketmq_message_consume_failures_total")
                .description("Total number of message consume failures")
                .tag("topic", topic)
                .tag("tag", tag)
                .tag("event_type", eventType)
                .tag("error_type", errorType)
                .register(meterRegistry)
                .increment();
    }

    /**
     * 记录消息重试
     */
    public void recordMessageRetry(String topic, String tag, String eventType, int retryTimes) {
        Counter.builder("rocketmq_message_retries_total")
                .description("Total number of message retries")
                .tag("topic", topic)
                .tag("tag", tag)
                .tag("event_type", eventType)
                .tag("retry_times", String.valueOf(retryTimes))
                .register(meterRegistry)
                .increment();
    }

    /**
     * 记录消息队列深度
     */
    public void recordQueueDepth(String topic, String consumerGroup, long depth) {
        meterRegistry.gauge("rocketmq_queue_depth", 
            io.micrometer.core.instrument.Tags.of("topic", topic, "consumer_group", consumerGroup),
            depth);
    }

    /**
     * 记录消费者延迟
     */
    public void recordConsumerLag(String topic, String consumerGroup, long lagMs) {
        meterRegistry.gauge("rocketmq_consumer_lag_ms", 
            io.micrometer.core.instrument.Tags.of("topic", topic, "consumer_group", consumerGroup),
            lagMs);
    }

    /**
     * 记录生产者TPS
     */
    public void recordProducerTps(String topic, double tps) {
        meterRegistry.gauge("rocketmq_producer_tps", 
            io.micrometer.core.instrument.Tags.of("topic", topic),
            tps);
    }

    /**
     * 记录消费者TPS
     */
    public void recordConsumerTps(String topic, String consumerGroup, double tps) {
        meterRegistry.gauge("rocketmq_consumer_tps", 
            io.micrometer.core.instrument.Tags.of("topic", topic, "consumer_group", consumerGroup),
            tps);
    }

    /**
     * 记录消息大小
     */
    public void recordMessageSize(String topic, String tag, long sizeBytes) {
        Timer.builder("rocketmq_message_size_bytes")
                .description("Message size in bytes")
                .tag("topic", topic)
                .tag("tag", tag)
                .register(meterRegistry)
                .record(sizeBytes, java.util.concurrent.TimeUnit.NANOSECONDS);
    }

    /**
     * 记录连接状态
     */
    public void recordConnectionStatus(String nameServer, boolean connected) {
        meterRegistry.gauge("rocketmq_connection_status", 
            io.micrometer.core.instrument.Tags.of("name_server", nameServer),
            connected ? 1 : 0);
    }
}
