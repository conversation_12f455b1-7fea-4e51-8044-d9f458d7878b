# Redis配置文件

# 网络配置
bind 0.0.0.0
port 6379
protected-mode no

# 通用配置
daemonize no
pidfile /var/run/redis_6379.pid
loglevel notice
logfile ""
databases 16

# 持久化配置
save 900 1
save 300 10
save 60 10000
stop-writes-on-bgsave-error yes
rdbcompression yes
rdbchecksum yes
dbfilename dump.rdb
dir /data

# 内存配置
maxmemory 256mb
maxmemory-policy allkeys-lru

# AOF配置
appendonly yes
appendfilename "appendonly.aof"
appendfsync everysec
no-appendfsync-on-rewrite no
auto-aof-rewrite-percentage 100
auto-aof-rewrite-min-size 64mb

# 慢查询配置
slowlog-log-slower-than 10000
slowlog-max-len 128

# 客户端配置
timeout 300
tcp-keepalive 300
tcp-backlog 511

# 安全配置
# requirepass yourpassword

# 其他配置
notify-keyspace-events ""
hash-max-ziplist-entries 512
hash-max-ziplist-value 64
list-max-ziplist-size -2
list-compress-depth 0
set-max-intset-entries 512
zset-max-ziplist-entries 128
zset-max-ziplist-value 64
hll-sparse-max-bytes 3000
stream-node-max-bytes 4096
stream-node-max-entries 100
activerehashing yes
client-output-buffer-limit normal 0 0 0
client-output-buffer-limit replica 256mb 64mb 60
client-output-buffer-limit pubsub 32mb 8mb 60
hz 10
dynamic-hz yes
aof-rewrite-incremental-fsync yes
rdb-save-incremental-fsync yes
