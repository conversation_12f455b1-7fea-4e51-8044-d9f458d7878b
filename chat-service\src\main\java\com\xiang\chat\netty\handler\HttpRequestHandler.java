package com.xiang.chat.netty.handler;

import io.netty.buffer.Unpooled;
import io.netty.channel.ChannelFutureListener;
import io.netty.channel.ChannelHandlerContext;
import io.netty.channel.DefaultFileRegion;
import io.netty.channel.SimpleChannelInboundHandler;
import io.netty.channel.ChannelHandler;
import io.netty.handler.codec.http.*;
import io.netty.util.CharsetUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.io.File;
import java.io.RandomAccessFile;

/**
 * HTTP请求处理器
 * 处理非WebSocket的HTTP请求，如静态文件、健康检查等
 */
@Slf4j
@Component
@ChannelHandler.Sharable
public class HttpRequestHandler extends SimpleChannelInboundHandler<FullHttpRequest> {

    @Value("${netty.websocket.path:/ws}")
    private String websocketPath;

    @Override
    protected void channelRead0(ChannelHandlerContext ctx, FullHttpRequest request) throws Exception {
        String uri = request.uri();
        String method = request.method().name();
        String remoteAddress = ctx.channel().remoteAddress().toString();

        log.info("收到HTTP请求: {} {}, remoteAddress={}, channelId={}",
            method, uri, remoteAddress, ctx.channel().id().asShortText());

        // 如果是WebSocket升级请求，传递给下一个处理器
        if (websocketPath.equals(uri)) {
            log.info("WebSocket升级请求: uri={}, channelId={}", uri, ctx.channel().id().asShortText());
            ctx.fireChannelRead(request.retain());
            return;
        }

        // 处理HTTP请求
        handleHttpRequest(ctx, request);
    }

    /**
     * 处理HTTP请求
     */
    private void handleHttpRequest(ChannelHandlerContext ctx, FullHttpRequest request) {
        String uri = request.uri();
        HttpMethod method = request.method();

        log.debug("收到HTTP请求: {} {}", method, uri);

        try {
            if (HttpMethod.GET.equals(method)) {
                handleGetRequest(ctx, request, uri);
            } else if (HttpMethod.POST.equals(method)) {
                handlePostRequest(ctx, request, uri);
            } else {
                sendMethodNotAllowed(ctx);
            }
        } catch (Exception e) {
            log.error("处理HTTP请求失败: {} {}", method, uri, e);
            sendInternalServerError(ctx, e.getMessage());
        }
    }

    /**
     * 处理GET请求
     */
    private void handleGetRequest(ChannelHandlerContext ctx, FullHttpRequest request, String uri) throws Exception {
        switch (uri) {
            case "/":
            case "/index.html":
                sendStaticFile(ctx, "static/index.html", "text/html");
                break;
            case "/health":
                sendHealthCheck(ctx);
                break;
            case "/info":
                sendServerInfo(ctx);
                break;
            default:
                if (uri.startsWith("/static/")) {
                    sendStaticFile(ctx, uri.substring(1), getContentType(uri));
                } else {
                    sendNotFound(ctx);
                }
        }
    }

    /**
     * 处理POST请求
     */
    private void handlePostRequest(ChannelHandlerContext ctx, FullHttpRequest request, String uri) {
        switch (uri) {
            case "/upload":
                handleFileUpload(ctx, request);
                break;
            default:
                sendNotFound(ctx);
        }
    }

    /**
     * 发送静态文件
     */
    private void sendStaticFile(ChannelHandlerContext ctx, String filePath, String contentType) throws Exception {
        // 这里简化处理，实际应用中应该有更完善的静态文件服务
        File file = new File(getClass().getClassLoader().getResource(filePath).getFile());
        
        if (!file.exists() || !file.isFile()) {
            sendNotFound(ctx);
            return;
        }

        RandomAccessFile raf = new RandomAccessFile(file, "r");
        long fileLength = raf.length();

        HttpResponse response = new DefaultHttpResponse(HttpVersion.HTTP_1_1, HttpResponseStatus.OK);
        HttpUtil.setContentLength(response, fileLength);
        response.headers().set(HttpHeaderNames.CONTENT_TYPE, contentType);
        response.headers().set(HttpHeaderNames.CACHE_CONTROL, "max-age=3600");

        ctx.write(response);

        // 发送文件内容
        ctx.writeAndFlush(new DefaultFileRegion(raf.getChannel(), 0, fileLength))
                .addListener(ChannelFutureListener.CLOSE);
    }

    /**
     * 发送健康检查响应
     */
    private void sendHealthCheck(ChannelHandlerContext ctx) {
        String content = "{\"status\":\"UP\",\"service\":\"chat-service\",\"timestamp\":" + System.currentTimeMillis() + "}";
        sendJsonResponse(ctx, HttpResponseStatus.OK, content);
    }

    /**
     * 发送服务器信息
     */
    private void sendServerInfo(ChannelHandlerContext ctx) {
        String content = "{\n" +
                "  \"service\": \"chat-service\",\n" +
                "  \"version\": \"1.0.0\",\n" +
                "  \"websocket\": {\n" +
                "    \"path\": \"" + websocketPath + "\",\n" +
                "    \"protocols\": [\"websocket\"]\n" +
                "  },\n" +
                "  \"timestamp\": " + System.currentTimeMillis() + "\n" +
                "}";
        sendJsonResponse(ctx, HttpResponseStatus.OK, content);
    }

    /**
     * 处理文件上传
     */
    private void handleFileUpload(ChannelHandlerContext ctx, FullHttpRequest request) {
        // 简化的文件上传处理
        String content = "{\"status\":\"success\",\"message\":\"文件上传功能待实现\"}";
        sendJsonResponse(ctx, HttpResponseStatus.OK, content);
    }

    /**
     * 发送JSON响应
     */
    private void sendJsonResponse(ChannelHandlerContext ctx, HttpResponseStatus status, String content) {
        FullHttpResponse response = new DefaultFullHttpResponse(
                HttpVersion.HTTP_1_1, status, Unpooled.copiedBuffer(content, CharsetUtil.UTF_8));
        
        response.headers().set(HttpHeaderNames.CONTENT_TYPE, "application/json; charset=UTF-8");
        response.headers().set(HttpHeaderNames.CONTENT_LENGTH, response.content().readableBytes());
        response.headers().set(HttpHeaderNames.ACCESS_CONTROL_ALLOW_ORIGIN, "*");
        response.headers().set(HttpHeaderNames.ACCESS_CONTROL_ALLOW_METHODS, "GET, POST, OPTIONS");
        response.headers().set(HttpHeaderNames.ACCESS_CONTROL_ALLOW_HEADERS, "Content-Type, Authorization");

        ctx.writeAndFlush(response).addListener(ChannelFutureListener.CLOSE);
    }

    /**
     * 发送404响应
     */
    private void sendNotFound(ChannelHandlerContext ctx) {
        String content = "{\"error\":\"Not Found\",\"code\":404}";
        sendJsonResponse(ctx, HttpResponseStatus.NOT_FOUND, content);
    }

    /**
     * 发送405响应
     */
    private void sendMethodNotAllowed(ChannelHandlerContext ctx) {
        String content = "{\"error\":\"Method Not Allowed\",\"code\":405}";
        sendJsonResponse(ctx, HttpResponseStatus.METHOD_NOT_ALLOWED, content);
    }

    /**
     * 发送500响应
     */
    private void sendInternalServerError(ChannelHandlerContext ctx, String message) {
        String content = "{\"error\":\"Internal Server Error\",\"message\":\"" + message + "\",\"code\":500}";
        sendJsonResponse(ctx, HttpResponseStatus.INTERNAL_SERVER_ERROR, content);
    }

    /**
     * 获取内容类型
     */
    private String getContentType(String uri) {
        if (uri.endsWith(".html")) {
            return "text/html; charset=UTF-8";
        } else if (uri.endsWith(".css")) {
            return "text/css; charset=UTF-8";
        } else if (uri.endsWith(".js")) {
            return "application/javascript; charset=UTF-8";
        } else if (uri.endsWith(".json")) {
            return "application/json; charset=UTF-8";
        } else if (uri.endsWith(".png")) {
            return "image/png";
        } else if (uri.endsWith(".jpg") || uri.endsWith(".jpeg")) {
            return "image/jpeg";
        } else if (uri.endsWith(".gif")) {
            return "image/gif";
        } else {
            return "application/octet-stream";
        }
    }
}
