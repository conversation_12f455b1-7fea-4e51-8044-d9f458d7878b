package com.xiang.chat.service;

import com.xiang.chat.model.dto.ChatRoomDTO;
import com.xiang.chat.model.entity.ChatRoomEntity;

import java.util.List;

/**
 * 聊天室服务接口
 */
public interface ChatRoomService {
    
    /**
     * 创建聊天室
     */
    ChatRoomEntity createRoom(ChatRoomDTO roomDTO);
    
    /**
     * 获取聊天室信息
     */
    ChatRoomEntity getRoomById(String roomId);
    
    /**
     * 更新聊天室信息
     */
    ChatRoomEntity updateRoom(String roomId, ChatRoomDTO roomDTO);
    
    /**
     * 删除聊天室
     */
    void deleteRoom(String roomId, Long operatorId);
    
    /**
     * 获取用户创建的聊天室列表
     */
    List<ChatRoomEntity> getUserCreatedRooms(Long userId);
    
    /**
     * 获取用户加入的聊天室列表
     */
    List<ChatRoomEntity> getUserJoinedRooms(Long userId);
    
    /**
     * 获取公开聊天室列表
     */
    List<ChatRoomEntity> getPublicRooms(int page, int size);
    
    /**
     * 搜索聊天室
     */
    List<ChatRoomEntity> searchRooms(String keyword, int page, int size);
    
    /**
     * 用户加入聊天室
     */
    void joinRoom(String roomId, Long userId);
    
    /**
     * 用户离开聊天室
     */
    void leaveRoom(String roomId, Long userId);
    
    /**
     * 检查用户是否可以加入聊天室
     */
    boolean canJoinRoom(String roomId, Long userId);
    
    /**
     * 获取聊天室成员列表
     */
    List<Long> getRoomMembers(String roomId);
    
    /**
     * 获取聊天室成员数量
     */
    int getRoomMemberCount(String roomId);
    
    /**
     * 踢出聊天室成员
     */
    void kickMember(String roomId, Long memberId, Long operatorId);
    
    /**
     * 设置聊天室管理员
     */
    void setRoomAdmin(String roomId, Long userId, Long operatorId);
    
    /**
     * 检查用户是否为聊天室管理员
     */
    boolean isRoomAdmin(String roomId, Long userId);
    
    /**
     * 检查用户是否为聊天室创建者
     */
    boolean isRoomCreator(String roomId, Long userId);
}
