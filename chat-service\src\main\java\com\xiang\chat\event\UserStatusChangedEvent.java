package com.xiang.chat.event;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 用户状态变更事件
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class UserStatusChangedEvent extends BaseEvent {

    private static final long serialVersionUID = 1L;

    /**
     * 用户ID
     */
    private Long targetUserId;

    /**
     * 状态 (ONLINE/OFFLINE)
     */
    private String status;

    /**
     * 节点ID
     */
    private String nodeId;

    /**
     * 时间戳
     */
    private Long timestamp;

    public UserStatusChangedEvent() {
        super();
    }

    public UserStatusChangedEvent(String source, String userId, Long targetUserId, String status, String nodeId) {
        super(source, userId);
        this.targetUserId = targetUserId;
        this.status = status;
        this.nodeId = nodeId;
        this.timestamp = System.currentTimeMillis();
    }
}