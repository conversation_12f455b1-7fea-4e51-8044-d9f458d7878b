package com.xiang.chat.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.xiang.chat.model.entity.ChatRoomEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.List;

/**
 * 聊天室Mapper
 */
@Mapper
public interface ChatRoomMapper extends BaseMapper<ChatRoomEntity> {
    
    /**
     * 获取用户创建的房间数量
     */
    @Select("SELECT COUNT(*) FROM chat_room WHERE creator_id = #{userId} AND deleted = 0")
    long countUserCreatedRooms(@Param("userId") Long userId);
    
    /**
     * 获取热门房间（按当前用户数排序）
     */
    @Select("SELECT * FROM chat_room WHERE room_type = 'public' AND status = 1 AND deleted = 0 " +
            "ORDER BY current_users DESC, create_time DESC LIMIT #{limit}")
    List<ChatRoomEntity> getHotRooms(@Param("limit") int limit);
    
    /**
     * 获取最新创建的房间
     */
    @Select("SELECT * FROM chat_room WHERE room_type = 'public' AND status = 1 AND deleted = 0 " +
            "ORDER BY create_time DESC LIMIT #{limit}")
    List<ChatRoomEntity> getLatestRooms(@Param("limit") int limit);
    
    /**
     * 根据房间类型统计数量
     */
    @Select("SELECT COUNT(*) FROM chat_room WHERE room_type = #{roomType} AND status = 1 AND deleted = 0")
    long countRoomsByType(@Param("roomType") String roomType);
    
    /**
     * 更新房间当前用户数
     */
    @Update("UPDATE chat_room SET current_users = #{currentUsers} WHERE room_id = #{roomId}")
    int updateRoomCurrentUsers(@Param("roomId") String roomId, @Param("currentUsers") Integer currentUsers);
    
    /**
     * 批量更新房间状态
     */
    @Update("UPDATE chat_room SET status = #{status} WHERE room_id IN " +
            "<foreach collection='roomIds' item='roomId' open='(' separator=',' close=')'>" +
            "#{roomId}" +
            "</foreach>")
    int batchUpdateRoomStatus(@Param("roomIds") List<String> roomIds, @Param("status") Integer status);
    
    /**
     * 搜索房间（模糊匹配房间名称和描述）
     */
    @Select("SELECT * FROM chat_room WHERE " +
            "(room_name LIKE CONCAT('%', #{keyword}, '%') OR description LIKE CONCAT('%', #{keyword}, '%')) " +
            "AND room_type = 'public' AND status = 1 AND deleted = 0 " +
            "ORDER BY current_users DESC, create_time DESC")
    List<ChatRoomEntity> searchRoomsByKeyword(@Param("keyword") String keyword);
}
