# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# Broker 基本配置
brokerClusterName=DefaultCluster
brokerName=broker-a
brokerId=0

# NameServer地址
namesrvAddr=rocketmq-nameserver:9876

# Broker对外服务的监听端口
listenPort=10911

# Broker对外服务的IP地址，如果不设置，会自动获取本机IP
# 在Docker环境中，需要设置为宿主机IP，以便外部应用连接
brokerIP1=*************

# 删除文件时间点，默认凌晨4点
deleteWhen=04

# 文件保留时间，默认72小时
fileReservedTime=72

# Broker的角色
# - ASYNC_MASTER 异步复制Master
# - SYNC_MASTER 同步双写Master
# - SLAVE
brokerRole=ASYNC_MASTER

# 刷盘方式
# - ASYNC_FLUSH 异步刷盘
# - SYNC_FLUSH 同步刷盘
flushDiskType=ASYNC_FLUSH

# 存储路径
storePathRootDir=/opt/store
storePathCommitLog=/opt/store/commitlog
storePathConsumeQueue=/opt/store/consumequeue
storePathIndex=/opt/store/index

# 确保目录权限正确
# 这些目录会在启动时由Docker命令创建并设置权限

# 限制的消息大小
maxMessageSize=65536

# 发送消息线程池数量
sendMessageThreadPoolNums=128

# 拉消息线程池数量
pullMessageThreadPoolNums=128

# 查询消息线程池数量
queryMessageThreadPoolNums=8

# 管理Broker线程池数量
adminBrokerThreadPoolNums=16

# 客户端管理线程池数量
clientManagerThreadPoolNums=32

# 消费者管理线程池数量
consumerManagerThreadPoolNums=32

# 心跳线程池数量
heartbeatThreadPoolNums=8

# 结束事务线程池数量
endTransactionThreadPoolNums=8

# 是否允许Broker自动创建Topic，建议线下开启，线上关闭
autoCreateTopicEnable=true

# 是否允许Broker自动创建订阅组，建议线下开启，线上关闭
autoCreateSubscriptionGroup=true

# Broker对外服务的监听端口
listenPort=10911

# 发送消息超时时间，默认3s
sendMessageTimeout=3000

# 拉取消息超时时间，默认15s
pullMessageTimeout=15000

# 消息最大重试次数
maxDelayTime=40

# 消息在队列中最大存储时间，超过则会被清理，单位秒
messageDelayLevel=1s 5s 10s 30s 1m 2m 3m 4m 5m 6m 7m 8m 9m 10m 20m 30m 1h 2h

# 是否启用属性过滤
enablePropertyFilter=false

# 是否支持SQL92过滤
enableSqlFilter=false

# 压缩消息
compressedRegister=false

# 是否启用DLQ
enableDLQStats=false

# 事务消息相关配置
transactionTimeOut=6000
transactionCheckMax=15
transactionCheckInterval=60000

# 访问消息在内存中的最大比例
accessMessageInMemoryMaxRatio=40

# 是否启用消息轨迹
traceOn=true

# 消息轨迹Topic
msgTraceTopicName=RMQ_SYS_TRACE_TOPIC

# 是否使用ReputMessageService
useReputMessageService=true

# 服务器异步发送消息
serverAsyncSemaphoreValue=64

# 服务器单向发送消息
serverOnewaySemaphoreValue=256

# 客户端异步发送消息
clientAsyncSemaphoreValue=65535

# 客户端单向发送消息
clientOnewaySemaphoreValue=65535

# 客户端回调执行线程数
clientCallbackExecutorThreads=8

# 是否启用消费队列扩展
enableConsumeQueueExt=false

# 映射文件大小
mappedFileSizeCommitLog=1073741824
mappedFileSizeConsumeQueue=300000

# 是否启用快速失败
fastFailIfNoBufferInStorePool=false

# 是否等待存储消息OK
waitTimeMillsInSendQueue=200

# 开启字节缓冲区重用
enableByteBufferReuse=false

# 传输数据是否开启堆外内存
transferMsgByHeap=true

# 最大传输字节数
maxTransferBytesOnMessageInMemory=262144

# 最大传输消息数量
maxTransferCountOnMessageInMemory=32

# 最大传输字节数在磁盘中
maxTransferBytesOnMessageInDisk=65536

# 最大传输消息数量在磁盘中
maxTransferCountOnMessageInDisk=8

# 是否启用堆外内存
enableDirectMemory=false

# 堆外内存大小
directMemorySize=21474836480

# 是否启用消息索引
messageIndexEnable=true

# 消息索引文件大小
maxIndexNum=20000000

# 最大哈希槽数量
maxHashSlotNum=5000000

# 索引文件数量
indexIntervalOfSameKey=4000

# 是否启用消息存储统计
enableMessageStoreStats=true
