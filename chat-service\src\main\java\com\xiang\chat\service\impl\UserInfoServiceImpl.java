package com.xiang.chat.service.impl;

import com.xiang.chat.dto.UserInfo;
import com.xiang.chat.service.UserInfoService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

/**
 * 用户信息服务实现
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class UserInfoServiceImpl implements UserInfoService {
    
    private final RestTemplate restTemplate;
    
    @Value("${auth.service.url:http://localhost:8080}")
    private String authServiceUrl;
    
    // 用户信息缓存，避免频繁调用auth-service
    private final Map<Long, UserInfo> userInfoCache = new ConcurrentHashMap<>();
    private final Map<Long, Long> cacheTimestamp = new ConcurrentHashMap<>();
    
    // 缓存过期时间：5分钟
    private static final long CACHE_EXPIRE_TIME = 5 * 60 * 1000;
    
    @Override
    public UserInfo getUserInfo(Long userId) {
        if (userId == null) {
            return null;
        }
        
        // 检查缓存
        UserInfo cachedInfo = getCachedUserInfo(userId);
        if (cachedInfo != null) {
            return cachedInfo;
        }
        
        try {
            // 调用auth-service获取用户信息
            String url = authServiceUrl + "/api/users/" + userId;
            
            HttpHeaders headers = new HttpHeaders();
            headers.set("Content-Type", "application/json");
            HttpEntity<String> entity = new HttpEntity<>(headers);
            
            ResponseEntity<Map> response = restTemplate.exchange(
                url, HttpMethod.GET, entity, Map.class);
            
            if (response.getStatusCode().is2xxSuccessful() && response.getBody() != null) {
                Map<String, Object> responseBody = response.getBody();
                Boolean success = (Boolean) responseBody.get("success");
                
                if (Boolean.TRUE.equals(success)) {
                    Map<String, Object> userData = (Map<String, Object>) responseBody.get("data");
                    if (userData != null) {
                        UserInfo userInfo = mapToUserInfo(userData);
                        // 缓存用户信息
                        cacheUserInfo(userId, userInfo);
                        return userInfo;
                    }
                }
            }
            
            log.warn("获取用户信息失败: userId={}", userId);
            
        } catch (Exception e) {
            log.error("调用auth-service获取用户信息异常: userId={}", userId, e);
        }
        
        // 返回默认用户信息
        UserInfo defaultInfo = createDefaultUserInfo(userId);
        cacheUserInfo(userId, defaultInfo);
        return defaultInfo;
    }
    
    @Override
    public Map<Long, UserInfo> getUserInfoBatch(Set<Long> userIds) {
        if (userIds == null || userIds.isEmpty()) {
            return new HashMap<>();
        }
        
        Map<Long, UserInfo> result = new HashMap<>();
        Set<Long> uncachedUserIds = new HashSet<>();
        
        // 先从缓存获取
        for (Long userId : userIds) {
            UserInfo cachedInfo = getCachedUserInfo(userId);
            if (cachedInfo != null) {
                result.put(userId, cachedInfo);
            } else {
                uncachedUserIds.add(userId);
            }
        }
        
        // 批量获取未缓存的用户信息
        if (!uncachedUserIds.isEmpty()) {
            try {
                String url = authServiceUrl + "/api/users/batch";
                
                HttpHeaders headers = new HttpHeaders();
                headers.set("Content-Type", "application/json");
                
                Map<String, Object> requestBody = new HashMap<>();
                requestBody.put("userIds", uncachedUserIds);
                
                HttpEntity<Map<String, Object>> entity = new HttpEntity<>(requestBody, headers);
                
                ResponseEntity<Map> response = restTemplate.exchange(
                    url, HttpMethod.POST, entity, Map.class);
                
                if (response.getStatusCode().is2xxSuccessful() && response.getBody() != null) {
                    Map<String, Object> responseBody = response.getBody();
                    Boolean success = (Boolean) responseBody.get("success");
                    
                    if (Boolean.TRUE.equals(success)) {
                        List<Map<String, Object>> userDataList = (List<Map<String, Object>>) responseBody.get("data");
                        if (userDataList != null) {
                            for (Map<String, Object> userData : userDataList) {
                                UserInfo userInfo = mapToUserInfo(userData);
                                result.put(userInfo.getId(), userInfo);
                                cacheUserInfo(userInfo.getId(), userInfo);
                            }
                        }
                    }
                }
                
            } catch (Exception e) {
                log.error("批量获取用户信息异常: userIds={}", uncachedUserIds, e);
            }
            
            // 为未获取到的用户创建默认信息
            for (Long userId : uncachedUserIds) {
                if (!result.containsKey(userId)) {
                    UserInfo defaultInfo = createDefaultUserInfo(userId);
                    result.put(userId, defaultInfo);
                    cacheUserInfo(userId, defaultInfo);
                }
            }
        }
        
        return result;
    }
    
    @Override
    public List<UserInfo> getUserInfoList(Set<Long> userIds) {
        Map<Long, UserInfo> userInfoMap = getUserInfoBatch(userIds);
        return userIds.stream()
            .map(userInfoMap::get)
            .filter(Objects::nonNull)
            .collect(Collectors.toList());
    }
    
    /**
     * 从缓存获取用户信息
     */
    private UserInfo getCachedUserInfo(Long userId) {
        Long timestamp = cacheTimestamp.get(userId);
        if (timestamp != null && System.currentTimeMillis() - timestamp < CACHE_EXPIRE_TIME) {
            return userInfoCache.get(userId);
        }
        
        // 缓存过期，清理
        userInfoCache.remove(userId);
        cacheTimestamp.remove(userId);
        return null;
    }
    
    /**
     * 缓存用户信息
     */
    private void cacheUserInfo(Long userId, UserInfo userInfo) {
        userInfoCache.put(userId, userInfo);
        cacheTimestamp.put(userId, System.currentTimeMillis());
    }
    
    /**
     * 将Map转换为UserInfo对象
     */
    private UserInfo mapToUserInfo(Map<String, Object> userData) {
        UserInfo userInfo = new UserInfo();
        userInfo.setId(Long.valueOf(userData.get("id").toString()));
        userInfo.setUsername((String) userData.get("username"));
        userInfo.setNickname((String) userData.get("nickname"));
        userInfo.setEmail((String) userData.get("email"));
        userInfo.setRole((String) userData.get("role"));
        userInfo.setAvatarUrl((String) userData.get("avatarUrl"));
        return userInfo;
    }
    
    /**
     * 创建默认用户信息
     */
    private UserInfo createDefaultUserInfo(Long userId) {
        UserInfo userInfo = new UserInfo();
        userInfo.setId(userId);
        userInfo.setUsername("用户" + userId);
        userInfo.setNickname("用户" + userId);
        userInfo.setEmail("");
        userInfo.setRole("USER");
        userInfo.setAvatarUrl("");
        return userInfo;
    }
}
