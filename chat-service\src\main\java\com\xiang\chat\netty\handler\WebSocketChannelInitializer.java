package com.xiang.chat.netty.handler;

import io.netty.channel.ChannelHandlerContext;
import io.netty.channel.ChannelInboundHandlerAdapter;
import io.netty.channel.ChannelInitializer;
import io.netty.channel.ChannelPipeline;
import io.netty.channel.socket.SocketChannel;
import io.netty.handler.codec.http.HttpObjectAggregator;
import io.netty.handler.codec.http.HttpRequest;
import io.netty.handler.codec.http.HttpServerCodec;
import io.netty.handler.codec.http.websocketx.WebSocketServerProtocolHandler;
import io.netty.handler.codec.http.websocketx.extensions.compression.WebSocketServerCompressionHandler;
import io.netty.handler.stream.ChunkedWriteHandler;
import io.netty.handler.timeout.IdleStateHandler;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.concurrent.TimeUnit;

/**
 * WebSocket通道初始化器
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class WebSocketChannelInitializer extends ChannelInitializer<SocketChannel> {

    @Value("${netty.websocket.path:/ws}")
    private String websocketPath;

    @Value("${netty.websocket.max-frame-size:65536}")
    private int maxFrameSize;

    private final WebSocketHandler webSocketHandler;
    private final HttpRequestHandler httpRequestHandler;

    @Override
    protected void initChannel(SocketChannel ch) throws Exception {
        try {
            log.info("初始化WebSocket通道: channelId={}, remoteAddress={}, localAddress={}, isActive={}",
                ch.id().asShortText(), ch.remoteAddress(), ch.localAddress(), ch.isActive());
            ChannelPipeline pipeline = ch.pipeline();

        // 添加连接状态监控
        pipeline.addFirst("connection-monitor", new ChannelInboundHandlerAdapter() {
            @Override
            public void channelActive(ChannelHandlerContext ctx) throws Exception {
                log.info("连接激活: channelId={}, remoteAddress={}",
                    ctx.channel().id().asShortText(), ctx.channel().remoteAddress());
                super.channelActive(ctx);
            }

            @Override
            public void channelInactive(ChannelHandlerContext ctx) throws Exception {
                log.info("连接断开: channelId={}, remoteAddress={}",
                    ctx.channel().id().asShortText(), ctx.channel().remoteAddress());
                super.channelInactive(ctx);
            }

            @Override
            public void exceptionCaught(ChannelHandlerContext ctx, Throwable cause) throws Exception {
                log.error("连接异常: channelId={}, remoteAddress={}, cause={}",
                    ctx.channel().id().asShortText(), ctx.channel().remoteAddress(), cause.getMessage(), cause);
                // 不调用 super.exceptionCaught，避免连接被立即关闭
                // super.exceptionCaught(ctx, cause);
            }
        });

        // 添加连接超时检测（10秒内必须完成握手，之后会被移除）
        IdleStateHandler handshakeTimeout = new IdleStateHandler(10, 0, 0, TimeUnit.SECONDS);
        pipeline.addLast("handshake-timeout", handshakeTimeout);
        log.info("已添加握手超时检测器: channelId={}, timeout=10秒", ch.id().asShortText());

        // HTTP编解码器
        pipeline.addLast("http-codec", new HttpServerCodec());

        // 添加调试处理器
        pipeline.addLast("debug-handler", new ChannelInboundHandlerAdapter() {
            @Override
            public void channelRead(ChannelHandlerContext ctx, Object msg) throws Exception {
                log.info("HTTP消息接收: channelId={}, msgType={}, msg={}",
                    ctx.channel().id().asShortText(), msg.getClass().getSimpleName(),
                    msg instanceof HttpRequest ? ((HttpRequest) msg).uri() : "non-http");
                super.channelRead(ctx, msg);
            }

            @Override
            public void exceptionCaught(ChannelHandlerContext ctx, Throwable cause) throws Exception {
                log.error("HTTP处理异常: channelId={}, cause={}",
                    ctx.channel().id().asShortText(), cause.getMessage(), cause);
                // 记录异常但不关闭连接
                // super.exceptionCaught(ctx, cause);
            }
        });
        
        // HTTP对象聚合器，将多个HTTP消息聚合成一个完整的HTTP消息
        pipeline.addLast("http-aggregator", new HttpObjectAggregator(maxFrameSize));
        
        // 支持大文件传输
        pipeline.addLast("http-chunked", new ChunkedWriteHandler());
        
        // 处理HTTP请求（如文件上传、API调用等）
        pipeline.addLast("http-handler", httpRequestHandler);
        
        // WebSocket压缩处理器
        pipeline.addLast("websocket-compression", new WebSocketServerCompressionHandler());
        
        // WebSocket协议处理器
        pipeline.addLast("websocket-protocol", new WebSocketServerProtocolHandler(
            websocketPath, null, true, maxFrameSize, false, true, 10000L));
        
        // 注意：心跳检测器会在认证成功后动态添加，这里不添加避免与握手超时检测器冲突
        // pipeline.addLast("idle-state", new IdleStateHandler(60, 30, 90, TimeUnit.SECONDS));
        
        // 自定义WebSocket处理器
        pipeline.addLast("websocket-handler", webSocketHandler);

        log.info("WebSocket通道初始化完成: channelId={}", ch.id().asShortText());

        } catch (Exception e) {
            log.error("WebSocket通道初始化失败: channelId={}, remoteAddress={}, cause={}",
                ch.id().asShortText(), ch.remoteAddress(), e.getMessage(), e);
            throw e; // 重新抛出异常
        }
    }
}
