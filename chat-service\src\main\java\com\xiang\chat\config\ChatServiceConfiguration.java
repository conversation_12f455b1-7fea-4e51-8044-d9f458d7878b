package com.xiang.chat.config;

import com.xiang.chat.service.ConnectionManager;
import com.xiang.chat.service.DistributedConnectionManager;
import com.xiang.chat.service.impl.ConnectionManagerImpl;
import jakarta.annotation.PostConstruct;
import org.springframework.context.annotation.Configuration;

/**
 * 聊天服务配置类
 * 解决循环依赖问题
 */
@Configuration
public class ChatServiceConfiguration {

    private final ConnectionManager connectionManager;
    private final DistributedConnectionManager distributedConnectionManager;

    public ChatServiceConfiguration(ConnectionManager connectionManager,
                                  DistributedConnectionManager distributedConnectionManager) {
        this.connectionManager = connectionManager;
        this.distributedConnectionManager = distributedConnectionManager;
    }

    @PostConstruct
    public void init() {
        // 解决循环依赖：在初始化后设置分布式连接管理器
        if (connectionManager instanceof ConnectionManagerImpl) {
            ((ConnectionManagerImpl) connectionManager).setDistributedConnectionManager(distributedConnectionManager);
        }
    }
}
