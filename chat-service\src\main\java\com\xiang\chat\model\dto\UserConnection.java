package com.xiang.chat.model.dto;

import io.netty.channel.Channel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 用户连接信息
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class UserConnection {
    
    /**
     * 用户ID
     */
    private Long userId;
    
    /**
     * 连接通道
     */
    private Channel channel;
    
    /**
     * 连接时间
     */
    private LocalDateTime connectTime;
    
    /**
     * 最后活跃时间
     */
    private LocalDateTime lastActiveTime;
    
    /**
     * 用户加入的房间列表
     */
    private Set<String> joinedRooms;
    
    /**
     * 用户属性（扩展信息）
     */
    private ConcurrentHashMap<String, Object> attributes;
    
    /**
     * 连接状态
     * CONNECTING: 连接中
     * CONNECTED: 已连接
     * AUTHENTICATED: 已认证
     * DISCONNECTED: 已断开
     */
    private ConnectionStatus status;
    
    /**
     * 用户IP地址
     */
    private String ipAddress;
    
    /**
     * 用户代理信息
     */
    private String userAgent;
    
    public UserConnection(Long userId, Channel channel) {
        this.userId = userId;
        this.channel = channel;
        this.connectTime = LocalDateTime.now();
        this.lastActiveTime = LocalDateTime.now();
        this.joinedRooms = ConcurrentHashMap.newKeySet();
        this.attributes = new ConcurrentHashMap<>();
        this.status = ConnectionStatus.CONNECTED;
    }
    
    /**
     * 更新最后活跃时间
     */
    public void updateLastActiveTime() {
        this.lastActiveTime = LocalDateTime.now();
    }
    
    /**
     * 加入房间
     */
    public void joinRoom(String roomId) {
        if (joinedRooms == null) {
            joinedRooms = ConcurrentHashMap.newKeySet();
        }
        joinedRooms.add(roomId);
    }
    
    /**
     * 离开房间
     */
    public void leaveRoom(String roomId) {
        if (joinedRooms != null) {
            joinedRooms.remove(roomId);
        }
    }
    
    /**
     * 检查是否在指定房间
     */
    public boolean isInRoom(String roomId) {
        return joinedRooms != null && joinedRooms.contains(roomId);
    }
    
    /**
     * 设置属性
     */
    public void setAttribute(String key, Object value) {
        if (attributes == null) {
            attributes = new ConcurrentHashMap<>();
        }
        attributes.put(key, value);
    }
    
    /**
     * 获取属性
     */
    public Object getAttribute(String key) {
        return attributes != null ? attributes.get(key) : null;
    }
    
    /**
     * 连接状态枚举
     */
    public enum ConnectionStatus {
        CONNECTING,
        CONNECTED,
        AUTHENTICATED,
        DISCONNECTED
    }
}
