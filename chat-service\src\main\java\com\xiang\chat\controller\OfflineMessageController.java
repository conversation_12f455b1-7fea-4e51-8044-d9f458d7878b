package com.xiang.chat.controller;

import com.xiang.chat.dto.WebSocketMessage;
import com.xiang.chat.result.Result;
import com.xiang.chat.service.OfflineMessageService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * 离线消息管理控制器
 */
@Slf4j
@RestController
@RequestMapping("/api/chat/offline")
@RequiredArgsConstructor
@Tag(name = "离线消息管理", description = "离线消息相关API")
public class OfflineMessageController {

    private final OfflineMessageService offlineMessageService;

    @GetMapping("/users/{userId}/messages")
    @Operation(summary = "获取用户离线消息")
    public Result<List<WebSocketMessage>> getOfflineMessages(
            @Parameter(description = "用户ID") @PathVariable Long userId) {
        List<WebSocketMessage> messages = offlineMessageService.getOfflineMessages(userId);
        return Result.success(messages);
    }

    @GetMapping("/users/{userId}/count")
    @Operation(summary = "获取用户离线消息数量")
    public Result<Long> getOfflineMessageCount(
            @Parameter(description = "用户ID") @PathVariable Long userId) {
        long count = offlineMessageService.getOfflineMessageCount(userId);
        return Result.success(count);
    }

    @PostMapping("/users/{userId}/send")
    @Operation(summary = "发送离线消息给用户")
    public Result<String> sendOfflineMessages(
            @Parameter(description = "用户ID") @PathVariable Long userId) {
        offlineMessageService.sendOfflineMessagesToUser(userId);
        return Result.success("离线消息发送任务已启动");
    }

    @DeleteMapping("/users/{userId}/messages")
    @Operation(summary = "清除用户离线消息")
    public Result<String> clearOfflineMessages(
            @Parameter(description = "用户ID") @PathVariable Long userId) {
        offlineMessageService.clearOfflineMessages(userId);
        return Result.success("离线消息已清除");
    }

    @GetMapping("/users/{userId}/stats")
    @Operation(summary = "获取用户离线消息统计")
    public Result<OfflineMessageService.OfflineMessageStats> getOfflineMessageStats(
            @Parameter(description = "用户ID") @PathVariable Long userId) {
        OfflineMessageService.OfflineMessageStats stats = offlineMessageService.getOfflineMessageStats(userId);
        return Result.success(stats);
    }

    @PostMapping("/users/{userId}/messages/{messageId}/sent")
    @Operation(summary = "标记离线消息为已发送")
    public Result<String> markOfflineMessageAsSent(
            @Parameter(description = "用户ID") @PathVariable Long userId,
            @Parameter(description = "消息ID") @PathVariable String messageId) {
        offlineMessageService.markOfflineMessageAsSent(userId, messageId);
        return Result.success("消息已标记为已发送");
    }

    @PostMapping("/cleanup")
    @Operation(summary = "清理过期离线消息")
    public Result<String> cleanupExpiredOfflineMessages() {
        offlineMessageService.cleanupExpiredOfflineMessages();
        return Result.success("过期离线消息清理任务已启动");
    }

    @GetMapping("/summary")
    @Operation(summary = "获取离线消息系统概览")
    public Result<Map<String, Object>> getOfflineMessageSummary() {
        // 这里可以实现系统级别的离线消息统计
        Map<String, Object> summary = Map.of(
                "description", "离线消息系统概览",
                "features", List.of(
                        "自动存储离线消息",
                        "用户上线时自动推送",
                        "消息去重和状态跟踪",
                        "过期消息自动清理",
                        "详细的统计信息"
                ),
                "storage", Map.of(
                        "type", "Redis",
                        "maxMessages", 1000,
                        "expireDays", 7
                ),
                "timestamp", System.currentTimeMillis()
        );
        
        return Result.success(summary);
    }
}
