package com.xiang.chat.model.dto;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 聊天室DTO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ChatRoomDTO {
    
    /**
     * 房间ID（创建时可为空，系统自动生成）
     */
    private String roomId;
    
    /**
     * 房间名称
     */
    @NotBlank(message = "房间名称不能为空")
    @Size(max = 50, message = "房间名称长度不能超过50个字符")
    private String roomName;
    
    /**
     * 房间描述
     */
    @Size(max = 200, message = "房间描述长度不能超过200个字符")
    private String description;
    
    /**
     * 房间类型
     * public: 公开房间
     * private: 私有房间
     * group: 群组房间
     */
    @NotBlank(message = "房间类型不能为空")
    private String roomType;
    
    /**
     * 房间创建者ID
     */
    private Long creatorId;
    
    /**
     * 最大用户数
     */
    @Min(value = 2, message = "最大用户数不能少于2")
    @Max(value = 500, message = "最大用户数不能超过500")
    private Integer maxUsers;
    
    /**
     * 房间状态
     * 0: 禁用
     * 1: 启用
     */
    private Integer status;
    
    /**
     * 房间配置（JSON格式）
     */
    private String config;
    
    /**
     * 是否需要密码
     */
    private Boolean requirePassword;
    
    /**
     * 房间密码
     */
    private String password;
    
    /**
     * 是否允许匿名用户
     */
    private Boolean allowAnonymous;
    
    /**
     * 消息保留天数
     */
    @Min(value = 1, message = "消息保留天数不能少于1天")
    @Max(value = 365, message = "消息保留天数不能超过365天")
    private Integer messageRetentionDays;
}
