-- =============================================
-- 用户表创建脚本
-- 用于OAuth2密码模式认证的聊天系统
-- =============================================

USE springcloud_chat;

-- 创建用户表
CREATE TABLE IF NOT EXISTS `user` (
    `id` BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    `username` VARCHAR(50) NOT NULL UNIQUE COMMENT '用户名（唯一）',
    `password` VARCHAR(255) NOT NULL COMMENT '密码（BCrypt加密）',
    `nickname` VARCHAR(100) NOT NULL COMMENT '昵称',
    `email` VARCHAR(100) NULL UNIQUE COMMENT '邮箱（唯一）',
    `phone` VARCHAR(20) NULL COMMENT '手机号',
    `role` VARCHAR(20) NOT NULL DEFAULT 'USER' COMMENT '角色：USER-普通用户,ADMIN-管理员',
    `status` TINYINT NOT NULL DEFAULT 1 COMMENT '状态：0-禁用,1-启用',
    `avatar_url` VARCHAR(500) NULL COMMENT '头像URL',
    `last_login_time` DATETIME(3) NULL COMMENT '最后登录时间',
    `last_login_ip` VARCHAR(45) NULL COMMENT '最后登录IP',
    `login_count` INT NOT NULL DEFAULT 0 COMMENT '登录次数',
    `create_time` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '创建时间',
    `update_time` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3) COMMENT '更新时间',
    `deleted` TINYINT NOT NULL DEFAULT 0 COMMENT '逻辑删除标志：0-未删除,1-已删除',
    
    -- 索引优化
    INDEX `idx_username` (`username`),
    INDEX `idx_email` (`email`),
    INDEX `idx_status_deleted` (`status`, `deleted`),
    INDEX `idx_role` (`role`),
    INDEX `idx_create_time` (`create_time` DESC),
    INDEX `idx_last_login` (`last_login_time` DESC),
    
    -- 约束检查
    CONSTRAINT `chk_user_role` CHECK (`role` IN ('USER', 'ADMIN')),
    CONSTRAINT `chk_user_status` CHECK (`status` IN (0, 1)),
    CONSTRAINT `chk_user_deleted` CHECK (`deleted` IN (0, 1))
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户表';

-- 创建用户配置表（扩展用户信息）
CREATE TABLE IF NOT EXISTS `user_profile` (
    `id` BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    `user_id` BIGINT NOT NULL UNIQUE COMMENT '用户ID',
    `real_name` VARCHAR(50) NULL COMMENT '真实姓名',
    `gender` TINYINT NULL COMMENT '性别：0-未知,1-男,2-女',
    `birthday` DATE NULL COMMENT '生日',
    `location` VARCHAR(100) NULL COMMENT '所在地',
    `signature` VARCHAR(200) NULL COMMENT '个性签名',
    `language` VARCHAR(10) NOT NULL DEFAULT 'zh-CN' COMMENT '语言偏好',
    `timezone` VARCHAR(50) NOT NULL DEFAULT 'Asia/Shanghai' COMMENT '时区',
    `theme` VARCHAR(20) NOT NULL DEFAULT 'light' COMMENT '主题：light-浅色,dark-深色',
    `notification_enabled` TINYINT NOT NULL DEFAULT 1 COMMENT '是否启用通知：0-否,1-是',
    `sound_enabled` TINYINT NOT NULL DEFAULT 1 COMMENT '是否启用声音：0-否,1-是',
    `create_time` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '创建时间',
    `update_time` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3) COMMENT '更新时间',
    
    -- 外键约束
    FOREIGN KEY `fk_profile_user` (`user_id`) REFERENCES `user`(`id`) ON DELETE CASCADE,
    
    -- 索引
    INDEX `idx_user_id` (`user_id`),
    INDEX `idx_location` (`location`),
    INDEX `idx_gender` (`gender`),
    
    -- 约束检查
    CONSTRAINT `chk_gender` CHECK (`gender` IN (0, 1, 2)),
    CONSTRAINT `chk_notification` CHECK (`notification_enabled` IN (0, 1)),
    CONSTRAINT `chk_sound` CHECK (`sound_enabled` IN (0, 1))
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户配置表';

-- 创建用户登录日志表
CREATE TABLE IF NOT EXISTS `user_login_log` (
    `id` BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    `user_id` BIGINT NOT NULL COMMENT '用户ID',
    `username` VARCHAR(50) NOT NULL COMMENT '用户名',
    `login_ip` VARCHAR(45) NOT NULL COMMENT '登录IP',
    `login_location` VARCHAR(100) NULL COMMENT '登录地点',
    `user_agent` VARCHAR(500) NULL COMMENT '用户代理',
    `login_type` VARCHAR(20) NOT NULL DEFAULT 'PASSWORD' COMMENT '登录类型：PASSWORD-密码,OAUTH2-第三方',
    `login_status` TINYINT NOT NULL COMMENT '登录状态：0-失败,1-成功',
    `failure_reason` VARCHAR(200) NULL COMMENT '失败原因',
    `login_time` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '登录时间',
    
    -- 索引
    INDEX `idx_user_time` (`user_id`, `login_time` DESC),
    INDEX `idx_username_time` (`username`, `login_time` DESC),
    INDEX `idx_login_ip` (`login_ip`),
    INDEX `idx_login_status` (`login_status`),
    INDEX `idx_login_time` (`login_time` DESC),
    
    -- 约束检查
    CONSTRAINT `chk_login_status` CHECK (`login_status` IN (0, 1))
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户登录日志表';

-- 创建用户会话表（用于管理用户在线状态）
CREATE TABLE IF NOT EXISTS `user_session` (
    `id` BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    `user_id` BIGINT NOT NULL COMMENT '用户ID',
    `session_id` VARCHAR(128) NOT NULL UNIQUE COMMENT '会话ID',
    `access_token` TEXT NULL COMMENT '访问令牌',
    `refresh_token` TEXT NULL COMMENT '刷新令牌',
    `token_expires_at` DATETIME(3) NULL COMMENT 'Token过期时间',
    `client_ip` VARCHAR(45) NOT NULL COMMENT '客户端IP',
    `user_agent` VARCHAR(500) NULL COMMENT '用户代理',
    `online_status` TINYINT NOT NULL DEFAULT 1 COMMENT '在线状态：0-离线,1-在线,2-忙碌,3-离开',
    `last_activity_time` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '最后活动时间',
    `create_time` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '创建时间',
    `update_time` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3) COMMENT '更新时间',
    
    -- 索引
    INDEX `idx_user_online` (`user_id`, `online_status`),
    INDEX `idx_session_id` (`session_id`),
    INDEX `idx_last_activity` (`last_activity_time` DESC),
    INDEX `idx_token_expires` (`token_expires_at`),
    
    -- 约束检查
    CONSTRAINT `chk_online_status` CHECK (`online_status` IN (0, 1, 2, 3))
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户会话表';