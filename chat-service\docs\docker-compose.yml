version: '3.8'

services:
  # MySQL数据库
  mysql:
    image: mysql:8.0
    container_name: springcloud-mysql
    restart: always
    environment:
      MYSQL_ROOT_PASSWORD: 123456
      MYSQL_DATABASE: springcloud_chat
      MYSQL_USER: springcloud
      MYSQL_PASSWORD: 123456
      TZ: Asia/Shanghai
    ports:
      - "3306:3306"
    volumes:
      - mysql_data:/var/lib/mysql
      - ./mysql/conf:/etc/mysql/conf.d
      - ./mysql/init:/docker-entrypoint-initdb.d
    command: --default-authentication-plugin=mysql_native_password
    networks:
      - springcloud-network

  # Redis缓存
  redis:
    image: redis:7.0-alpine
    container_name: springcloud-redis
    restart: always
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
      - ./redis/redis.conf:/etc/redis/redis.conf
    command: redis-server /etc/redis/redis.conf
    networks:
      - springcloud-network

  # Nacos注册中心
  nacos:
    image: nacos/nacos-server:v2.1.2
    container_name: springcloud-nacos
    restart: always
    environment:
      MODE: standalone
      SPRING_DATASOURCE_PLATFORM: mysql
      MYSQL_SERVICE_HOST: mysql
      MYSQL_SERVICE_PORT: 3306
      MYSQL_SERVICE_DB_NAME: nacos_config
      MYSQL_SERVICE_USER: root
      MYSQL_SERVICE_PASSWORD: 123456
      NACOS_AUTH_ENABLE: true
      NACOS_AUTH_TOKEN: SecretKey012345678901234567890123456789012345678901234567890123456789
      NACOS_AUTH_IDENTITY_KEY: nacos
      NACOS_AUTH_IDENTITY_VALUE: nacos
      JVM_XMS: 512m
      JVM_XMX: 512m
      JVM_XMN: 256m
      JAVA_OPT_EXT: "-Dmanagement.metrics.binders.processor.enabled=false -Djdk.attach.allowAttachSelf=true"
    ports:
      - "8848:8848"
      - "9848:9848"
    volumes:
      - nacos_data:/home/<USER>/data
      - nacos_logs:/home/<USER>/logs
    depends_on:
      - mysql
    networks:
      - springcloud-network

  # RocketMQ NameServer
  rocketmq-nameserver:
    image: apache/rocketmq:5.1.0
    container_name: springcloud-rocketmq-nameserver
    restart: always
    user: root
    ports:
      - "9876:9876"
    volumes:
      - ./rocketmq_nameserver_logs:/opt/logs
      - ./rocketmq_nameserver_store:/opt/store
    environment:
      JAVA_OPT: "-Duser.home=/opt -Xms256M -Xmx512M -Xmn128m"
    ulimits:
      nofile:
        soft: 65536
        hard: 65536
      nproc: 4096
    deploy:
      resources:
        limits:
          memory: 1G
          cpus: '1.0'
        reservations:
          memory: 256M
          cpus: '0.25'
    command: |
      sh -c '
        chown -R rocketmq:rocketmq /opt/logs /opt/store
        mkdir -p /opt/logs/rocketmqlogs
        mkdir -p /opt/store
        su rocketmq -c "sh mqnamesrv"
      '
    networks:
      - springcloud-network
    healthcheck:
      test: ["CMD-SHELL", "nc -z localhost 9876 || exit 1"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 30s

  # RocketMQ Broker
  rocketmq-broker:
    image: apache/rocketmq:5.1.0
    container_name: springcloud-rocketmq-broker
    restart: always
    user: root
    ports:
      - "10909:10909"
      - "10911:10911"
    volumes:
      - ./rocketmq_broker_logs:/opt/logs
      - ./rocketmq_broker_store:/opt/store
      - ./rocketmq/broker.conf:/opt/rocketmq-5.1.4/conf/broker.conf
    environment:
      NAMESRV_ADDR: "rocketmq-nameserver:9876"
      JAVA_OPT: "-Duser.home=/opt -Xms256M -Xmx512M -Xmn128m"
    ulimits:
      nofile:
        soft: 65536
        hard: 65536
      nproc: 4096
    deploy:
      resources:
        limits:
          memory: 1G
          cpus: '1.0'
        reservations:
          memory: 256M
          cpus: '0.5'
    command: |
      sh -c '
        chown -R rocketmq:rocketmq /opt/logs /opt/store
        mkdir -p /opt/logs/rocketmqlogs
        mkdir -p /opt/store/commitlog
        mkdir -p /opt/store/consumequeue
        mkdir -p /opt/store/index
        su rocketmq -c "sh mqbroker -c /opt/rocketmq-5.1.4/conf/broker.conf"
      '
    depends_on:
      rocketmq-nameserver:
        condition: service_healthy
    networks:
      - springcloud-network
    healthcheck:
      test: ["CMD-SHELL", "nc -z localhost 10911 || exit 1"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 60s

  # RocketMQ Console (可选的管理界面)
  rocketmq-console:
    image: styletang/rocketmq-console-ng:1.0.0
    container_name: springcloud-rocketmq-console
    restart: always
    ports:
      - "8180:8080"
    environment:
      JAVA_OPTS: "-Xms256m -Xmx512m -Drocketmq.namesrv.addr=rocketmq-nameserver:9876 -Dcom.rocketmq.sendMessageWithVIPChannel=false"
    ulimits:
      nofile:
        soft: 65536
        hard: 65536
      nproc: 4096
    deploy:
      resources:
        limits:
          memory: 1G
          cpus: '1.0'
        reservations:
          memory: 256M
          cpus: '0.5'
    depends_on:
      rocketmq-nameserver:
        condition: service_healthy
    networks:
      - springcloud-network
    healthcheck:
      test: ["CMD-SHELL", "curl -f http://localhost:8080 || exit 1"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s

# 数据卷
volumes:
  mysql_data:
    driver: local
  redis_data:
    driver: local
  nacos_data:
    driver: local
  nacos_logs:
    driver: local

# 网络
networks:
  springcloud-network:
    driver: bridge
