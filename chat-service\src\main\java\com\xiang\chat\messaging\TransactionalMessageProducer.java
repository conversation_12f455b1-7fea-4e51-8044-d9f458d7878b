package com.xiang.chat.messaging;

import com.alibaba.fastjson2.JSON;
import com.xiang.chat.constants.MessageConstants;
import com.xiang.chat.event.BaseEvent;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.client.producer.LocalTransactionState;
import org.apache.rocketmq.client.producer.TransactionListener;
import org.apache.rocketmq.client.producer.TransactionMQProducer;
import org.apache.rocketmq.client.producer.TransactionSendResult;
import org.apache.rocketmq.common.message.Message;
import org.apache.rocketmq.common.message.MessageExt;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.PreDestroy;
import java.nio.charset.StandardCharsets;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

/**
 * 事务消息生产者
 */
@Slf4j
@Component
@RequiredArgsConstructor
@ConditionalOnClass(TransactionMQProducer.class)
public class TransactionalMessageProducer implements TransactionListener {

    private TransactionMQProducer transactionProducer;
    private ExecutorService executorService;

    @Value("${rocketmq.name-server}")
    private String nameServerAddr;

    // 存储本地事务状态
    private final ConcurrentHashMap<String, LocalTransactionState> localTransactionStates = new ConcurrentHashMap<>();

    @PostConstruct
    public void init() {
        try {
            // 创建事务消息生产者
            transactionProducer = new TransactionMQProducer("transaction-producer-group");
            transactionProducer.setNamesrvAddr(nameServerAddr);
            
            // 设置事务监听器
            transactionProducer.setTransactionListener(this);
            
            // 创建线程池用于执行本地事务
            executorService = Executors.newFixedThreadPool(10);
            transactionProducer.setExecutorService(executorService);
            
            // 启动生产者
            transactionProducer.start();
            
            log.info("事务消息生产者启动成功");
        } catch (Exception e) {
            log.error("事务消息生产者启动失败", e);
            throw new RuntimeException("事务消息生产者启动失败", e);
        }
    }

    @PreDestroy
    public void destroy() {
        try {
            if (transactionProducer != null) {
                transactionProducer.shutdown();
            }
            if (executorService != null) {
                executorService.shutdown();
            }
            log.info("事务消息生产者关闭成功");
        } catch (Exception e) {
            log.error("事务消息生产者关闭失败", e);
        }
    }

    /**
     * 发送事务消息
     */
    public TransactionSendResult sendTransactionalMessage(String topic, String tag, BaseEvent event, Object localTransactionArg) {
        try {

            // 构建消息
            Message message = buildMessage(topic, tag, event);
            
            // 发送事务消息
            TransactionSendResult result = transactionProducer.sendMessageInTransaction(message, localTransactionArg);
            
            log.info("事务消息发送成功: topic={}, tag={}, messageId={}, localTransactionState={}",
                topic, tag, result.getMsgId(), result.getLocalTransactionState());
            
            return result;
        } catch (Exception e) {
            log.error("事务消息发送失败: topic={}, tag={}, eventType={}",
                topic, tag, event.getEventType(), e);
            throw new RuntimeException("发送事务消息失败", e);
        }
    }

    /**
     * 执行本地事务
     */
    @Override
    public LocalTransactionState executeLocalTransaction(Message msg, Object arg) {
        String transactionId = msg.getTransactionId();
        
        try {

            // 执行本地事务逻辑
            LocalTransactionState state = executeBusinessTransaction(msg, arg);
            
            // 存储事务状态
            localTransactionStates.put(transactionId, state);
            

            log.info("本地事务执行完成: transactionId={}, state={}", transactionId, state);
            
            return state;
        } catch (Exception e) {
            log.error("本地事务执行失败: transactionId={}", transactionId, e);
            
            // 事务执行失败，回滚
            localTransactionStates.put(transactionId, LocalTransactionState.ROLLBACK_MESSAGE);
            return LocalTransactionState.ROLLBACK_MESSAGE;
        }
    }

    /**
     * 检查本地事务状态
     */
    @Override
    public LocalTransactionState checkLocalTransaction(MessageExt msg) {
        String transactionId = msg.getTransactionId();
        
        try {

            log.info("检查本地事务状态: transactionId={}, messageId={}", transactionId, msg.getMsgId());
            
            // 从存储中获取事务状态
            LocalTransactionState state = localTransactionStates.get(transactionId);
            
            if (state == null) {
                // 如果找不到事务状态，可以查询数据库或其他持久化存储
                state = checkBusinessTransactionState(msg);
                if (state != null) {
                    localTransactionStates.put(transactionId, state);
                }
            }
            
            // 如果仍然无法确定状态，返回UNKNOW，RocketMQ会继续回查
            if (state == null) {
                state = LocalTransactionState.UNKNOW;
            }
            

            log.info("本地事务状态检查完成: transactionId={}, state={}", transactionId, state);
            
            return state;
        } catch (Exception e) {
            log.error("本地事务状态检查失败: transactionId={}", transactionId, e);
            
            // 检查失败，返回UNKNOW，让RocketMQ继续回查
            return LocalTransactionState.UNKNOW;
        }
    }

    /**
     * 执行业务事务逻辑
     * 子类可以重写此方法实现具体的业务逻辑
     */
    protected LocalTransactionState executeBusinessTransaction(Message msg, Object arg) {
        // 默认实现：模拟业务逻辑
        try {
            // 这里应该执行具体的业务逻辑
            // 比如：数据库操作、调用其他服务等
            
            // 模拟业务处理时间
            Thread.sleep(100);
            
            // 模拟90%的成功率
            if (Math.random() < 0.9) {
                return LocalTransactionState.COMMIT_MESSAGE;
            } else {
                return LocalTransactionState.ROLLBACK_MESSAGE;
            }
        } catch (Exception e) {
            log.error("业务事务执行失败", e);
            return LocalTransactionState.ROLLBACK_MESSAGE;
        }
    }

    /**
     * 检查业务事务状态
     * 子类可以重写此方法实现具体的状态检查逻辑
     */
    protected LocalTransactionState checkBusinessTransactionState(MessageExt msg) {
        // 默认实现：从数据库或其他存储中查询事务状态
        // 这里应该根据具体业务逻辑实现
        return null;
    }

    /**
     * 构建消息
     */
    private Message buildMessage(String topic, String tag, BaseEvent event) {
        String messageBody = JSON.toJSONString(event);
        Message message = new Message(topic, tag, messageBody.getBytes(StandardCharsets.UTF_8));
        
        // 设置消息属性
        message.putUserProperty(MessageConstants.PROPERTY_EVENT_TYPE, event.getEventType());
        message.putUserProperty(MessageConstants.PROPERTY_SOURCE, event.getSource());
        message.putUserProperty(MessageConstants.PROPERTY_TRACE_ID, event.getTraceId());
        if (event.getUserId() != null) {
            message.putUserProperty(MessageConstants.PROPERTY_USER_ID, event.getUserId().toString());
        }
        
        return message;
    }
}
