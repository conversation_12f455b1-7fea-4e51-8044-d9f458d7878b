package com.xiang.chat.service.impl;

import cn.hutool.core.util.IdUtil;
import com.alibaba.fastjson2.JSON;
import com.xiang.chat.config.ChatProperties;
import com.xiang.chat.exception.BusinessException;
import com.xiang.chat.service.FileUploadService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.imageio.ImageIO;
import java.awt.*;
import java.awt.image.BufferedImage;
import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * 文件上传服务实现
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class FileUploadServiceImpl implements FileUploadService {

    private final ChatProperties chatProperties;
    private final RedisTemplate<String, Object> redisTemplate;

    // 支持的图片格式
    private static final String[] IMAGE_EXTENSIONS = {"jpg", "jpeg", "png", "gif", "bmp", "webp"};
    
    // 支持的文档格式
    private static final String[] DOCUMENT_EXTENSIONS = {"pdf", "doc", "docx", "xls", "xlsx", "ppt", "pptx", "txt"};
    
    // 支持的音频格式
    private static final String[] AUDIO_EXTENSIONS = {"mp3", "wav", "aac", "ogg", "m4a"};
    
    // 支持的视频格式
    private static final String[] VIDEO_EXTENSIONS = {"mp4", "avi", "mov", "wmv", "flv", "mkv"};

    @Override
    public FileUploadResult uploadChatFile(MultipartFile file, Long userId, String roomId) {
        try {
            // 验证文件
            validateFile(file);
            
            // 生成文件ID和路径
            String fileId = IdUtil.fastSimpleUUID();
            String fileName = generateFileName(file.getOriginalFilename());
            String filePath = generateFilePath("chat", fileName);
            
            // 保存文件
            saveFile(file, filePath);
            
            // 生成缩略图（如果是图片）
            String thumbnailUrl = null;
            if (isImageFile(file)) {
                thumbnailUrl = generateThumbnail(filePath, fileId);
            }
            
            // 创建文件信息
            FileInfo fileInfo = createFileInfo(fileId, file, fileName, filePath, userId, roomId, thumbnailUrl);
            
            // 缓存文件信息
            cacheFileInfo(fileId, fileInfo);
            
            // 创建上传结果
            FileUploadResult result = new FileUploadResult();
            result.setFileId(fileId);
            result.setFileName(fileName);
            result.setOriginalName(file.getOriginalFilename());
            result.setFileType(getFileExtension(file.getOriginalFilename()));
            result.setFileSize(file.getSize());
            result.setDownloadUrl(generateDownloadUrl(fileId));
            result.setThumbnailUrl(thumbnailUrl);
            result.setMetadata(extractMetadata(file));
            result.setUploadTime(System.currentTimeMillis());
            

            log.info("聊天文件上传成功: fileId={}, fileName={}, userId={}, roomId={}", 
                fileId, fileName, userId, roomId);
            
            return result;
            
        } catch (Exception e) {
            log.error("聊天文件上传失败: userId={}, roomId={}", userId, roomId, e);
            throw new BusinessException("文件上传失败: " + e.getMessage());
        }
    }

    @Override
    public FileUploadResult uploadAvatarFile(MultipartFile file, Long userId) {
        try {
            // 验证文件（头像只允许图片）
            validateAvatarFile(file);
            
            // 生成文件ID和路径
            String fileId = IdUtil.fastSimpleUUID();
            String fileName = generateFileName(file.getOriginalFilename());
            String filePath = generateFilePath("avatar", fileName);
            
            // 保存文件
            saveFile(file, filePath);
            
            // 生成头像缩略图
            String thumbnailUrl = generateAvatarThumbnail(filePath, fileId);
            
            // 创建文件信息
            FileInfo fileInfo = createFileInfo(fileId, file, fileName, filePath, userId, null, thumbnailUrl);
            
            // 缓存文件信息
            cacheFileInfo(fileId, fileInfo);
            
            // 创建上传结果
            FileUploadResult result = new FileUploadResult();
            result.setFileId(fileId);
            result.setFileName(fileName);
            result.setOriginalName(file.getOriginalFilename());
            result.setFileType(getFileExtension(file.getOriginalFilename()));
            result.setFileSize(file.getSize());
            result.setDownloadUrl(generateDownloadUrl(fileId));
            result.setThumbnailUrl(thumbnailUrl);
            result.setUploadTime(System.currentTimeMillis());
            

            log.info("头像文件上传成功: fileId={}, fileName={}, userId={}", fileId, fileName, userId);
            
            return result;
            
        } catch (Exception e) {
            log.error("头像文件上传失败: userId={}", userId, e);
            throw new BusinessException("头像上传失败: " + e.getMessage());
        }
    }

    @Override
    public boolean deleteFile(String fileId, Long userId) {
        try {
            FileInfo fileInfo = getFileInfo(fileId);
            if (fileInfo == null) {
                return false;
            }
            
            // 检查权限（只有上传者可以删除）
            if (!fileInfo.getUploaderId().equals(userId)) {
                throw new BusinessException("无权限删除此文件");
            }
            
            // 删除物理文件
            try {
                Files.deleteIfExists(Paths.get(fileInfo.getFilePath()));
                
                // 删除缩略图
                if (fileInfo.getThumbnailUrl() != null) {
                    String thumbnailPath = getThumbnailPath(fileId);
                    Files.deleteIfExists(Paths.get(thumbnailPath));
                }
            } catch (IOException e) {
                log.warn("删除物理文件失败: fileId={}, path={}", fileId, fileInfo.getFilePath(), e);
            }
            
            // 标记为已删除
            fileInfo.setDeleted(true);
            cacheFileInfo(fileId, fileInfo);
            
            log.info("文件删除成功: fileId={}, userId={}", fileId, userId);
            return true;
            
        } catch (Exception e) {
            log.error("文件删除失败: fileId={}, userId={}", fileId, userId, e);
            return false;
        }
    }

    @Override
    public FileInfo getFileInfo(String fileId) {
        try {
            String cacheKey = "chat:file:info:" + fileId;
            Object cached = redisTemplate.opsForValue().get(cacheKey);
            
            if (cached != null) {
                return JSON.parseObject(cached.toString(), FileInfo.class);
            }
            
            return null;
        } catch (Exception e) {
            log.error("获取文件信息失败: fileId={}", fileId, e);
            return null;
        }
    }

    @Override
    public String getFileDownloadUrl(String fileId) {
        return generateDownloadUrl(fileId);
    }

    @Override
    public boolean fileExists(String fileId) {
        FileInfo fileInfo = getFileInfo(fileId);
        return fileInfo != null && !fileInfo.isDeleted();
    }

    /**
     * 验证文件
     */
    private void validateFile(MultipartFile file) {
        if (file == null || file.isEmpty()) {
            throw new BusinessException("文件不能为空");
        }
        
        // 检查文件大小
        long maxSize = parseSize(chatProperties.getFile().getMaxSize());
        if (file.getSize() > maxSize) {
            throw new BusinessException("文件大小超过限制: " + chatProperties.getFile().getMaxSize());
        }
        
        // 检查文件类型
        String extension = getFileExtension(file.getOriginalFilename());
        String[] allowedTypes = chatProperties.getFile().getAllowedTypes().split(",");
        
        boolean isAllowed = Arrays.stream(allowedTypes)
            .anyMatch(type -> type.trim().equalsIgnoreCase(extension));
            
        if (!isAllowed) {
            throw new BusinessException("不支持的文件类型: " + extension);
        }
    }

    /**
     * 验证头像文件
     */
    private void validateAvatarFile(MultipartFile file) {
        if (file == null || file.isEmpty()) {
            throw new BusinessException("头像文件不能为空");
        }
        
        // 头像文件大小限制（2MB）
        if (file.getSize() > 2 * 1024 * 1024) {
            throw new BusinessException("头像文件大小不能超过2MB");
        }
        
        // 只允许图片格式
        String extension = getFileExtension(file.getOriginalFilename());
        boolean isImage = Arrays.stream(IMAGE_EXTENSIONS)
            .anyMatch(ext -> ext.equalsIgnoreCase(extension));
            
        if (!isImage) {
            throw new BusinessException("头像只支持图片格式");
        }
    }

    /**
     * 生成文件名
     */
    private String generateFileName(String originalFilename) {
        String extension = getFileExtension(originalFilename);
        return IdUtil.fastSimpleUUID() + "." + extension;
    }

    /**
     * 生成文件路径
     */
    private String generateFilePath(String category, String fileName) {
        String uploadPath = chatProperties.getFile().getUploadPath();
        String datePath = LocalDate.now().format(DateTimeFormatter.ofPattern("yyyy/MM/dd"));
        
        Path fullPath = Paths.get(uploadPath, category, datePath);
        
        // 创建目录
        try {
            log.debug("创建上传目录: {}", fullPath.toString());
            Files.createDirectories(fullPath);
            log.debug("上传目录创建成功: {}", fullPath.toString());
        } catch (IOException e) {
            log.error("创建上传目录失败: path={}, error={}", fullPath.toString(), e.getMessage(), e);
            throw new BusinessException("创建上传目录失败: " + fullPath.toString() + " - " + e.getMessage());
        }
        
        String finalPath = fullPath.resolve(fileName).toString();
        log.debug("生成文件路径: {}", finalPath);
        return finalPath;
    }

    /**
     * 保存文件
     */
    private void saveFile(MultipartFile file, String filePath) throws IOException {
        file.transferTo(new File(filePath));
    }

    /**
     * 获取文件扩展名
     */
    private String getFileExtension(String filename) {
        if (!StringUtils.hasText(filename)) {
            return "";
        }
        
        int lastDotIndex = filename.lastIndexOf('.');
        if (lastDotIndex == -1) {
            return "";
        }
        
        return filename.substring(lastDotIndex + 1).toLowerCase();
    }

    /**
     * 判断是否为图片文件
     */
    private boolean isImageFile(MultipartFile file) {
        String extension = getFileExtension(file.getOriginalFilename());
        return Arrays.stream(IMAGE_EXTENSIONS)
            .anyMatch(ext -> ext.equalsIgnoreCase(extension));
    }

    /**
     * 生成缩略图
     */
    private String generateThumbnail(String filePath, String fileId) {
        try {
            BufferedImage originalImage = ImageIO.read(new File(filePath));
            if (originalImage == null) {
                return null;
            }
            
            // 生成缩略图（200x200）
            BufferedImage thumbnail = createThumbnail(originalImage, 200, 200);
            
            // 保存缩略图
            String thumbnailPath = getThumbnailPath(fileId);
            Files.createDirectories(Paths.get(thumbnailPath).getParent());
            ImageIO.write(thumbnail, "jpg", new File(thumbnailPath));
            
            return generateThumbnailUrl(fileId);
            
        } catch (Exception e) {
            log.error("生成缩略图失败: filePath={}, fileId={}", filePath, fileId, e);
            return null;
        }
    }

    /**
     * 生成头像缩略图
     */
    private String generateAvatarThumbnail(String filePath, String fileId) {
        try {
            BufferedImage originalImage = ImageIO.read(new File(filePath));
            if (originalImage == null) {
                return null;
            }
            
            // 生成头像缩略图（100x100）
            BufferedImage thumbnail = createThumbnail(originalImage, 100, 100);
            
            // 保存缩略图
            String thumbnailPath = getThumbnailPath(fileId);
            Files.createDirectories(Paths.get(thumbnailPath).getParent());
            ImageIO.write(thumbnail, "jpg", new File(thumbnailPath));
            
            return generateThumbnailUrl(fileId);
            
        } catch (Exception e) {
            log.error("生成头像缩略图失败: filePath={}, fileId={}", filePath, fileId, e);
            return null;
        }
    }

    /**
     * 创建缩略图
     */
    private BufferedImage createThumbnail(BufferedImage originalImage, int width, int height) {
        BufferedImage thumbnail = new BufferedImage(width, height, BufferedImage.TYPE_INT_RGB);
        Graphics2D g2d = thumbnail.createGraphics();
        
        // 设置渲染质量
        g2d.setRenderingHint(RenderingHints.KEY_INTERPOLATION, RenderingHints.VALUE_INTERPOLATION_BILINEAR);
        g2d.setRenderingHint(RenderingHints.KEY_RENDERING, RenderingHints.VALUE_RENDER_QUALITY);
        g2d.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);
        
        // 绘制缩略图
        g2d.drawImage(originalImage, 0, 0, width, height, null);
        g2d.dispose();
        
        return thumbnail;
    }

    /**
     * 创建文件信息
     */
    private FileInfo createFileInfo(String fileId, MultipartFile file, String fileName, 
                                  String filePath, Long userId, String roomId, String thumbnailUrl) {
        FileInfo fileInfo = new FileInfo();
        fileInfo.setFileId(fileId);
        fileInfo.setFileName(fileName);
        fileInfo.setOriginalName(file.getOriginalFilename());
        fileInfo.setFileType(getFileExtension(file.getOriginalFilename()));
        fileInfo.setFileSize(file.getSize());
        fileInfo.setUploaderId(userId);
        fileInfo.setRoomId(roomId);
        fileInfo.setFilePath(filePath);
        fileInfo.setDownloadUrl(generateDownloadUrl(fileId));
        fileInfo.setThumbnailUrl(thumbnailUrl);
        fileInfo.setMetadata(extractMetadata(file));
        fileInfo.setUploadTime(System.currentTimeMillis());
        fileInfo.setDownloadCount(0);
        fileInfo.setDeleted(false);
        
        return fileInfo;
    }

    /**
     * 缓存文件信息
     */
    private void cacheFileInfo(String fileId, FileInfo fileInfo) {
        try {
            String cacheKey = "chat:file:info:" + fileId;
            redisTemplate.opsForValue().set(cacheKey, JSON.toJSONString(fileInfo), 7, TimeUnit.DAYS);
        } catch (Exception e) {
            log.error("缓存文件信息失败: fileId={}", fileId, e);
        }
    }

    /**
     * 提取文件元数据
     */
    private Map<String, Object> extractMetadata(MultipartFile file) {
        Map<String, Object> metadata = new HashMap<>();
        metadata.put("contentType", file.getContentType());
        metadata.put("uploadTime", System.currentTimeMillis());
        
        // 根据文件类型提取特定元数据
        String extension = getFileExtension(file.getOriginalFilename());
        
        if (Arrays.stream(IMAGE_EXTENSIONS).anyMatch(ext -> ext.equalsIgnoreCase(extension))) {
            metadata.put("category", "image");
        } else if (Arrays.stream(DOCUMENT_EXTENSIONS).anyMatch(ext -> ext.equalsIgnoreCase(extension))) {
            metadata.put("category", "document");
        } else if (Arrays.stream(AUDIO_EXTENSIONS).anyMatch(ext -> ext.equalsIgnoreCase(extension))) {
            metadata.put("category", "audio");
        } else if (Arrays.stream(VIDEO_EXTENSIONS).anyMatch(ext -> ext.equalsIgnoreCase(extension))) {
            metadata.put("category", "video");
        } else {
            metadata.put("category", "other");
        }
        
        return metadata;
    }

    /**
     * 生成下载URL
     */
    private String generateDownloadUrl(String fileId) {
        return "/api/chat/files/" + fileId + "/download";
    }

    /**
     * 生成缩略图URL
     */
    private String generateThumbnailUrl(String fileId) {
        return "/api/chat/files/" + fileId + "/thumbnail";
    }

    /**
     * 获取缩略图路径
     */
    private String getThumbnailPath(String fileId) {
        String uploadPath = chatProperties.getFile().getUploadPath();
        return Paths.get(uploadPath, "thumbnails", fileId + ".jpg").toString();
    }

    /**
     * 解析文件大小
     */
    private long parseSize(String sizeStr) {
        if (sizeStr == null || sizeStr.trim().isEmpty()) {
            return 10 * 1024 * 1024; // 默认10MB
        }
        
        sizeStr = sizeStr.trim().toUpperCase();
        long multiplier = 1;
        
        if (sizeStr.endsWith("KB")) {
            multiplier = 1024;
            sizeStr = sizeStr.substring(0, sizeStr.length() - 2);
        } else if (sizeStr.endsWith("MB")) {
            multiplier = 1024 * 1024;
            sizeStr = sizeStr.substring(0, sizeStr.length() - 2);
        } else if (sizeStr.endsWith("GB")) {
            multiplier = 1024 * 1024 * 1024;
            sizeStr = sizeStr.substring(0, sizeStr.length() - 2);
        }
        
        try {
            return Long.parseLong(sizeStr.trim()) * multiplier;
        } catch (NumberFormatException e) {
            return 10 * 1024 * 1024; // 默认10MB
        }
    }
}
