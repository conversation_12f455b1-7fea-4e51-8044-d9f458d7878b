package com.xiang.chat.service;

import com.xiang.chat.model.dto.ChatMessage;
import com.xiang.chat.model.entity.ChatMessageEntity;

import java.util.List;

/**
 * 聊天消息服务接口
 */
public interface ChatMessageService {
    
    /**
     * 处理聊天消息
     */
    void handleChatMessage(ChatMessage chatMessage);
    
    /**
     * 发送私聊消息
     */
    void sendPrivateMessage(ChatMessage chatMessage);
    
    /**
     * 发送群聊消息
     */
    void sendGroupMessage(ChatMessage chatMessage);
    
    /**
     * 保存消息到数据库
     */
    ChatMessageEntity saveMessage(ChatMessage chatMessage);
    
    /**
     * 获取聊天历史记录
     */
    List<ChatMessageEntity> getChatHistory(String roomId, Long userId, int page, int size);
    
    /**
     * 获取私聊历史记录
     */
    List<ChatMessageEntity> getPrivateChatHistory(Long senderId, Long receiverId, int page, int size);
    
    /**
     * 标记消息为已读
     */
    void markMessageAsRead(String messageId, Long userId);
    
    /**
     * 获取未读消息数量
     */
    long getUnreadMessageCount(Long userId);
    
    /**
     * 删除消息
     */
    void deleteMessage(String messageId, Long userId);
    
    /**
     * 撤回消息
     */
    void recallMessage(String messageId, Long userId);
    
    /**
     * 获取被引用的消息
     */
    ChatMessageEntity getReferencedMessage(String messageId);
    
    /**
     * 获取消息的回复列表
     */
    List<ChatMessageEntity> getMessageReplies(String messageId, int page, int size);
    
    /**
     * 获取消息的完整信息（包括引用消息的详细信息）
     */
    ChatMessage getMessageWithReferenceInfo(String messageId);
    
    /**
     * 获取带有完整引用信息的聊天历史
     */
    List<ChatMessage> getChatHistoryWithReferenceInfo(String roomId, Long userId, int page, int size);
}
