package com.xiang.chat.model.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.xiang.springcloudmybaits.config.base.BaseEntity;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 聊天文件实体
 */
@Data
@TableName("chat_files")
public class ChatFileEntity extends BaseEntity {

    /**
     * 文件ID
     */
    @TableField("file_id")
    private String fileId;

    /**
     * 原始文件名
     */
    @TableField("original_name")
    private String originalName;

    /**
     * 存储文件名
     */
    @TableField("stored_name")
    private String storedName;

    /**
     * 文件路径
     */
    @TableField("file_path")
    private String filePath;

    /**
     * 文件大小（字节）
     */
    @TableField("file_size")
    private Long fileSize;

    /**
     * 文件类型
     */
    @TableField("file_type")
    private String fileType;

    /**
     * MIME类型
     */
    @TableField("mime_type")
    private String mimeType;

    /**
     * 文件MD5
     */
    @TableField("file_md5")
    private String fileMd5;

    /**
     * 上传用户ID
     */
    @TableField("uploader_id")
    private Long uploaderId;

    /**
     * 关联消息ID
     */
    @TableField("message_id")
    private String messageId;

    /**
     * 房间ID（如果是群聊文件）
     */
    @TableField("room_id")
    private String roomId;

    /**
     * 文件状态：1-正常，2-已删除，3-审核中，4-审核失败
     */
    @TableField("status")
    private Integer status;

    /**
     * 下载次数
     */
    @TableField("download_count")
    private Integer downloadCount;

    /**
     * 是否公开：0-私有，1-公开
     */
    @TableField("is_public")
    private Integer isPublic;

    /**
     * 过期时间
     */
    @TableField("expire_time")
    private LocalDateTime expireTime;
}
