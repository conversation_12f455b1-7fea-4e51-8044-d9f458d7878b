package com.xiang.controller;

import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

@Slf4j
@RestController
@RequestMapping("/api")
public class TokenController {

    @PostMapping("/token/password")
    public ResponseEntity<?> getTokenByPassword(@RequestBody Map<String, String> request) {
        log.info("Password grant token request: {}", request);
        
        String username = request.get("username");
        String password = request.get("password");
        
        if (username == null || password == null) {
            return ResponseEntity.badRequest().body(Map.of(
                    "error", "invalid_request",
                    "error_description", "Username and password are required"
            ));
        }
        
        // 返回如何使用标准OAuth2端点的说明
        return ResponseEntity.ok(Map.of(
                "message", "Please use standard OAuth2 token endpoint",
                "endpoint", "POST /oauth2/token",
                "content_type", "application/x-www-form-urlencoded",
                "authorization", "Basic " + java.util.Base64.getEncoder().encodeToString("chat-client:chat-secret".getBytes()),
                "body", "grant_type=password&username=" + username + "&password=" + password + "&scope=read write",
                "example_curl", "curl -X POST http://localhost:9000/oauth2/token " +
                        "-H 'Content-Type: application/x-www-form-urlencoded' " +
                        "-H 'Authorization: Basic Y2hhdC1jbGllbnQ6Y2hhdC1zZWNyZXQ=' " +
                        "-d 'grant_type=password&username=" + username + "&password=" + password + "&scope=read write'"
        ));
    }
}