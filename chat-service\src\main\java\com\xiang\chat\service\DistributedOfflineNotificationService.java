package com.xiang.chat.service;

import com.xiang.chat.event.ChatMessageEvent;
import com.xiang.chat.model.dto.ChatMessage;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.script.DefaultRedisScript;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.concurrent.TimeUnit;

/**
 * 分布式离线通知服务
 * 解决分布式环境下离线消息重复处理的问题
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class DistributedOfflineNotificationService {

    private final DistributedConnectionManager distributedConnectionManager;
    private final OfflineMessageService offlineMessageService;
    private final RedisTemplate<String, Object> redisTemplate;

    // Redis分布式锁脚本
    private static final String LOCK_SCRIPT = 
        "if redis.call('get', KEYS[1]) == ARGV[1] then " +
        "return redis.call('del', KEYS[1]) " +
        "else " +
        "return 0 " +
        "end";

    private static final DefaultRedisScript<Long> UNLOCK_SCRIPT = new DefaultRedisScript<>(LOCK_SCRIPT, Long.class);

    /**
     * 处理私聊消息的离线通知
     * 使用分布式锁确保只有一个节点处理
     */
    public void handlePrivateMessageOfflineNotification(ChatMessageEvent event) {
        if (!event.getIsPrivate() || event.getReceiverId() == null) {
            return;
        }

        String lockKey = "chat:offline:lock:private:" + event.getMessageId();
        String lockValue = distributedConnectionManager.getCurrentNodeId() + ":" + System.currentTimeMillis();

        try {
            // 尝试获取分布式锁，锁定30秒
            Boolean lockAcquired = redisTemplate.opsForValue().setIfAbsent(lockKey, lockValue, 30, TimeUnit.SECONDS);
            
            if (Boolean.TRUE.equals(lockAcquired)) {
                log.debug("获取离线通知处理锁成功: messageId={}, nodeId={}", 
                    event.getMessageId(), distributedConnectionManager.getCurrentNodeId());
                
                try {
                    // 再次检查用户是否在线（双重检查）
                    boolean isOnlineGlobally = distributedConnectionManager.isUserOnlineGlobally(event.getReceiverId());
                    
                    if (!isOnlineGlobally) {
                        // 用户确实离线，发送推送通知
                        sendOfflinePushNotification(event);
                        log.info("私聊离线推送通知已发送: receiverId={}, messageId={}", 
                            event.getReceiverId(), event.getMessageId());
                    } else {
                        log.debug("用户已上线，取消离线推送通知: receiverId={}, messageId={}", 
                            event.getReceiverId(), event.getMessageId());
                    }
                } finally {
                    // 释放锁
                    releaseLock(lockKey, lockValue);
                }
            } else {
                log.debug("其他节点正在处理离线通知: messageId={}, currentNode={}", 
                    event.getMessageId(), distributedConnectionManager.getCurrentNodeId());
            }
            
        } catch (Exception e) {
            log.error("处理私聊离线通知失败: messageId={}", event.getMessageId(), e);
            // 确保释放锁
            releaseLock(lockKey, lockValue);
        }
    }

    /**
     * 处理群聊@提及的离线通知
     */
    public void handleMentionOfflineNotification(ChatMessageEvent event, Long mentionedUserId) {
        String lockKey = "chat:offline:lock:mention:" + event.getMessageId() + ":" + mentionedUserId;
        String lockValue = distributedConnectionManager.getCurrentNodeId() + ":" + System.currentTimeMillis();

        try {
            // 尝试获取分布式锁
            Boolean lockAcquired = redisTemplate.opsForValue().setIfAbsent(lockKey, lockValue, 30, TimeUnit.SECONDS);
            
            if (Boolean.TRUE.equals(lockAcquired)) {
                try {
                    // 检查被@用户是否在线
                    boolean isOnlineGlobally = distributedConnectionManager.isUserOnlineGlobally(mentionedUserId);
                    
                    if (!isOnlineGlobally) {
                        // 用户离线，发送@提及推送通知
                        sendMentionPushNotification(event, mentionedUserId);
                        log.info("@提及离线推送通知已发送: mentionedUserId={}, messageId={}", 
                            mentionedUserId, event.getMessageId());
                    } else {
                        log.debug("被@用户已在线，跳过推送通知: mentionedUserId={}, messageId={}", 
                            mentionedUserId, event.getMessageId());
                    }
                } finally {
                    releaseLock(lockKey, lockValue);
                }
            } else {
                log.debug("其他节点正在处理@提及通知: messageId={}, mentionedUserId={}", 
                    event.getMessageId(), mentionedUserId);
            }
            
        } catch (Exception e) {
            log.error("处理@提及离线通知失败: messageId={}, mentionedUserId={}", 
                event.getMessageId(), mentionedUserId, e);
            releaseLock(lockKey, lockValue);
        }
    }

    /**
     * 发送离线推送通知
     */
    private void sendOfflinePushNotification(ChatMessageEvent event) {
        try {
            // 1. 存储离线消息
            ChatMessage chatMessage = convertEventToChatMessage(event);
            offlineMessageService.storeOfflineChatMessage(event.getReceiverId(), chatMessage);

            // 2. 记录推送日志
            String pushKey = "chat:push:private:" + event.getReceiverId() + ":" + System.currentTimeMillis();
            redisTemplate.opsForHash().put(pushKey, "messageId", event.getMessageId());
            redisTemplate.opsForHash().put(pushKey, "senderId", event.getSenderId());
            redisTemplate.opsForHash().put(pushKey, "type", "offline_private_message");
            redisTemplate.opsForHash().put(pushKey, "nodeId", distributedConnectionManager.getCurrentNodeId());
            redisTemplate.opsForHash().put(pushKey, "timestamp", System.currentTimeMillis());
            redisTemplate.expire(pushKey, 7, TimeUnit.DAYS);

            // 3. TODO: 这里应该调用实际的推送服务（如APNs、FCM等）
            log.info("离线消息已存储，推送通知记录已保存: receiverId={}, messageId={}",
                event.getReceiverId(), event.getMessageId());

        } catch (Exception e) {
            log.error("发送离线推送通知失败: messageId={}", event.getMessageId(), e);
        }
    }

    /**
     * 发送@提及推送通知
     */
    private void sendMentionPushNotification(ChatMessageEvent event, Long mentionedUserId) {
        try {
            // 记录推送日志
            String pushKey = "chat:push:mention:" + mentionedUserId + ":" + System.currentTimeMillis();
            redisTemplate.opsForHash().put(pushKey, "messageId", event.getMessageId());
            redisTemplate.opsForHash().put(pushKey, "senderId", event.getSenderId());
            redisTemplate.opsForHash().put(pushKey, "roomId", event.getRoomId());
            redisTemplate.opsForHash().put(pushKey, "mentionedUserId", mentionedUserId);
            redisTemplate.opsForHash().put(pushKey, "type", "offline_mention");
            redisTemplate.opsForHash().put(pushKey, "nodeId", distributedConnectionManager.getCurrentNodeId());
            redisTemplate.opsForHash().put(pushKey, "timestamp", System.currentTimeMillis());
            redisTemplate.expire(pushKey, 7, TimeUnit.DAYS);
            
            // TODO: 这里应该调用实际的推送服务
            log.info("@提及推送通知记录已保存: mentionedUserId={}, messageId={}", 
                mentionedUserId, event.getMessageId());
            
        } catch (Exception e) {
            log.error("发送@提及推送通知失败: messageId={}, mentionedUserId={}", 
                event.getMessageId(), mentionedUserId, e);
        }
    }

    /**
     * 释放分布式锁
     */
    private void releaseLock(String lockKey, String lockValue) {
        try {
            redisTemplate.execute(UNLOCK_SCRIPT, Collections.singletonList(lockKey), lockValue);
            log.debug("分布式锁已释放: lockKey={}", lockKey);
        } catch (Exception e) {
            log.warn("释放分布式锁失败: lockKey={}", lockKey, e);
        }
    }

    /**
     * 检查消息是否已经被处理过
     */
    public boolean isMessageAlreadyProcessed(String messageId, String processType) {
        String processKey = "chat:processed:" + processType + ":" + messageId;
        return Boolean.TRUE.equals(redisTemplate.hasKey(processKey));
    }

    /**
     * 标记消息已处理
     */
    public void markMessageAsProcessed(String messageId, String processType) {
        String processKey = "chat:processed:" + processType + ":" + messageId;
        redisTemplate.opsForValue().set(processKey, distributedConnectionManager.getCurrentNodeId(), 1, TimeUnit.HOURS);
    }

    /**
     * 将ChatMessageEvent转换为ChatMessage
     */
    private ChatMessage convertEventToChatMessage(ChatMessageEvent event) {
        return ChatMessage.builder()
                .messageId(event.getMessageId())
                .senderId(event.getSenderId())
                .receiverId(event.getReceiverId())
                .roomId(event.getRoomId())
                .messageType(event.getMessageType())
                .content(event.getContent())
                .sendTime(System.currentTimeMillis())
                .status(1) // 已发送状态
                .build();
    }
}
