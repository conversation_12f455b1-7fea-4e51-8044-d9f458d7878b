package com.xiang.chat.event;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 聊天消息事件
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class ChatMessageEvent extends BaseEvent {
    
    /**
     * 消息ID
     */
    private String messageId;
    
    /**
     * 发送者ID
     */
    private Long senderId;
    
    /**
     * 接收者ID（私聊时使用）
     */
    private Long receiverId;
    
    /**
     * 房间ID（群聊时使用）
     */
    private String roomId;
    
    /**
     * 消息类型
     */
    private String messageType;
    
    /**
     * 消息内容
     */
    private String content;
    
    /**
     * 消息长度
     */
    private Integer contentLength;
    
    /**
     * 是否为私聊消息
     */
    private Boolean isPrivate;
    
    public ChatMessageEvent() {
        super("CHAT_MESSAGE", "chat-service");
    }
    
    public ChatMessageEvent(String messageId, Long senderId, Long receiverId,
                           String roomId, String messageType, String content) {
        this();
        this.messageId = messageId;
        this.senderId = senderId;
        this.receiverId = receiverId;
        this.roomId = roomId;
        this.messageType = messageType;
        this.content = content;
        this.contentLength = content != null ? content.length() : 0;
        this.isPrivate = receiverId != null;
        this.setUserId(senderId+"");
    }
}
