package com.xiang.controller;

import com.xiang.code.R;
import com.xiang.dto.RegisterRequest;
import com.xiang.dto.RegisterResponse;
import com.xiang.entity.UserEntity;
import com.xiang.service.UserService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

/**
 * 用户注册控制器
 */
@Slf4j
@RestController
@RequestMapping("/api")
@RequiredArgsConstructor
public class RegisterController {

    private final UserService userService;

    /**
     * 用户注册
     */
    @PostMapping("/register")
    public R<RegisterResponse> register(@Valid @RequestBody RegisterRequest registerRequest) {
        try {
            log.info("User registration request: username={}, email={}", 
                    registerRequest.getUsername(), registerRequest.getEmail());
            
            UserEntity user = userService.registerUser(registerRequest);
            
            RegisterResponse response = RegisterResponse.success(
                    user.getId(),
                    user.getUsername(),
                    user.getNickname(),
                    user.getEmail()
            );
            
            log.info("User registered successfully: id={}, username={}", user.getId(), user.getUsername());
            return R.success(response);
            
        } catch (Exception e) {
            log.error("User registration failed: {}", e.getMessage(), e);
            return R.error(e.getMessage());
        }
    }

    /**
     * 检查用户名是否可用
     */
    @GetMapping("/register/check-username")
    public R<Boolean> checkUsername(@RequestParam String username) {
        try {
            boolean exists = userService.existsByUsername(username);
            return R.success(!exists);
        } catch (Exception e) {
            log.error("Check username failed: {}", e.getMessage(), e);
            return R.error("检查用户名失败");
        }
    }

    /**
     * 检查邮箱是否可用
     */
    @GetMapping("/register/check-email")
    public R<Boolean> checkEmail(@RequestParam String email) {
        try {
            boolean exists = userService.existsByEmail(email);
            return R.success(!exists);
        } catch (Exception e) {
            log.error("Check email failed: {}", e.getMessage(), e);
            return R.error("检查邮箱失败");
        }
    }
}
