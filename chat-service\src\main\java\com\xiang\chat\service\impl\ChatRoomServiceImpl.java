package com.xiang.chat.service.impl;

import cn.hutool.core.util.IdUtil;
import com.alibaba.fastjson2.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.xiang.chat.exception.BusinessException;
import com.xiang.chat.mapper.ChatRoomMapper;
import com.xiang.chat.model.dto.ChatRoomDTO;
import com.xiang.chat.model.entity.ChatRoomEntity;
import com.xiang.chat.service.ChatRoomService;
import com.xiang.chat.service.ConnectionManager;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * 聊天室服务实现
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ChatRoomServiceImpl implements ChatRoomService {

    private final ChatRoomMapper chatRoomMapper;
    private final ConnectionManager connectionManager;
    private final RedisTemplate<String, Object> redisTemplate;

    @Override
    @Transactional
    public ChatRoomEntity createRoom(ChatRoomDTO roomDTO) {
        try {
            // 生成房间ID
            String roomId = roomDTO.getRoomId();
            if (roomId == null || roomId.trim().isEmpty()) {
                roomId = generateRoomId();
            }

            // 检查房间ID是否已存在
            if (getRoomById(roomId) != null) {
                throw new BusinessException("房间ID已存在: " + roomId);
            }

            // 创建房间实体
            ChatRoomEntity entity = ChatRoomEntity.builder()
                    .roomId(roomId)
                    .roomName(roomDTO.getRoomName())
                    .description(roomDTO.getDescription())
                    .roomType(roomDTO.getRoomType())
                    .creatorId(roomDTO.getCreatorId())
                    .maxUsers(roomDTO.getMaxUsers() != null ? roomDTO.getMaxUsers() : 100)
                    .currentUsers(0)
                    .status(roomDTO.getStatus() != null ? roomDTO.getStatus() : 1)
                    .config(buildRoomConfig(roomDTO))
                    .build();

            // 保存到数据库
            chatRoomMapper.insert(entity);

            // 缓存到Redis
            cacheRoomInfo(entity);

            log.info("聊天室创建成功: roomId={}, roomName={}, creatorId={}",
                roomId, roomDTO.getRoomName(), roomDTO.getCreatorId());

            return entity;
        } catch (Exception e) {
            log.error("创建聊天室失败: {}", roomDTO, e);
            throw e;
        }
    }

    @Override
    public ChatRoomEntity getRoomById(String roomId) {
        // 先从缓存获取
        ChatRoomEntity cached = getCachedRoomInfo(roomId);
        if (cached != null) {
            return cached;
        }

        // 从数据库获取
        LambdaQueryWrapper<ChatRoomEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ChatRoomEntity::getRoomId, roomId);
        ChatRoomEntity entity = chatRoomMapper.selectOne(wrapper);

        // 缓存到Redis
        if (entity != null) {
            cacheRoomInfo(entity);
        }

        return entity;
    }

    @Override
    @Transactional
    public ChatRoomEntity updateRoom(String roomId, ChatRoomDTO roomDTO) {
        ChatRoomEntity entity = getRoomById(roomId);
        if (entity == null) {
            throw new BusinessException("聊天室不存在: " + roomId);
        }

        // 更新字段
        if (roomDTO.getRoomName() != null) {
            entity.setRoomName(roomDTO.getRoomName());
        }
        if (roomDTO.getDescription() != null) {
            entity.setDescription(roomDTO.getDescription());
        }
        if (roomDTO.getMaxUsers() != null) {
            entity.setMaxUsers(roomDTO.getMaxUsers());
        }
        if (roomDTO.getStatus() != null) {
            entity.setStatus(roomDTO.getStatus());
        }
        if (roomDTO.getConfig() != null) {
            entity.setConfig(roomDTO.getConfig());
        }

        // 更新数据库
        chatRoomMapper.updateById(entity);

        // 更新缓存
        cacheRoomInfo(entity);

        log.info("聊天室更新成功: roomId={}", roomId);
        return entity;
    }

    @Override
    @Transactional
    public void deleteRoom(String roomId, Long operatorId) {
        ChatRoomEntity entity = getRoomById(roomId);
        if (entity == null) {
            throw new BusinessException("聊天室不存在: " + roomId);
        }

        // 检查权限
        if (!entity.getCreatorId().equals(operatorId)) {
            throw new BusinessException("只有房间创建者可以删除房间");
        }

        // 删除数据库记录
        chatRoomMapper.deleteById(entity.getId());

        // 清除缓存
        clearRoomCache(roomId);

        // 踢出所有用户
        Set<Long> roomUsers = connectionManager.getRoomUsers(roomId);
        for (Long userId : roomUsers) {
            // 发送房间解散通知
            connectionManager.removeUserFromRoom(roomId, userId);
            log.debug("用户被移出已删除的房间: userId={}, roomId={}", userId, roomId);
        }

        log.info("聊天室删除成功: roomId={}, operatorId={}", roomId, operatorId);
    }

    @Override
    public List<ChatRoomEntity> getUserCreatedRooms(Long userId) {
        LambdaQueryWrapper<ChatRoomEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ChatRoomEntity::getCreatorId, userId)
                .eq(ChatRoomEntity::getStatus, 1)
                .orderByDesc(ChatRoomEntity::getCreateTime);
        return chatRoomMapper.selectList(wrapper);
    }

    @Override
    public List<ChatRoomEntity> getUserJoinedRooms(Long userId) {
        // 从连接管理器获取用户加入的房间
        Set<String> roomIds = connectionManager.getUserRooms(userId);
        if (roomIds.isEmpty()) {
            return new ArrayList<>();
        }

        LambdaQueryWrapper<ChatRoomEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(ChatRoomEntity::getRoomId, roomIds)
                .eq(ChatRoomEntity::getStatus, 1)
                .orderByDesc(ChatRoomEntity::getCreateTime);
        return chatRoomMapper.selectList(wrapper);
    }

    @Override
    public List<ChatRoomEntity> getPublicRooms(int page, int size) {
        Page<ChatRoomEntity> pageParam = new Page<>(page, size);
        LambdaQueryWrapper<ChatRoomEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ChatRoomEntity::getRoomType, "public")
                .eq(ChatRoomEntity::getStatus, 1)
                .orderByDesc(ChatRoomEntity::getCurrentUsers)
                .orderByDesc(ChatRoomEntity::getCreateTime);

        Page<ChatRoomEntity> result = chatRoomMapper.selectPage(pageParam, wrapper);
        return result.getRecords();
    }

    @Override
    public List<ChatRoomEntity> searchRooms(String keyword, int page, int size) {
        Page<ChatRoomEntity> pageParam = new Page<>(page, size);
        LambdaQueryWrapper<ChatRoomEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.and(w -> w.like(ChatRoomEntity::getRoomName, keyword)
                        .or().like(ChatRoomEntity::getDescription, keyword))
                .eq(ChatRoomEntity::getRoomType, "public")
                .eq(ChatRoomEntity::getStatus, 1)
                .orderByDesc(ChatRoomEntity::getCurrentUsers);

        Page<ChatRoomEntity> result = chatRoomMapper.selectPage(pageParam, wrapper);
        return result.getRecords();
    }

    @Override
    @Transactional
    public void joinRoom(String roomId, Long userId) {
        ChatRoomEntity room = getRoomById(roomId);
        if (room == null) {
            throw new BusinessException("聊天室不存在: " + roomId);
        }

        if (room.getStatus() != 1) {
            throw new BusinessException("聊天室已禁用");
        }

        if (!canJoinRoom(roomId, userId)) {
            throw new BusinessException("无法加入聊天室");
        }

        // 检查房间人数限制
        int currentUsers = getRoomMemberCount(roomId);
        if (currentUsers >= room.getMaxUsers()) {
            throw new BusinessException("聊天室人数已满");
        }

        // 更新房间当前用户数
        room.setCurrentUsers(currentUsers + 1);
        chatRoomMapper.updateById(room);

        // 更新缓存
        cacheRoomInfo(room);

        // 添加到房间成员列表
        addRoomMember(roomId, userId);

        log.info("用户加入聊天室: userId={}, roomId={}", userId, roomId);
    }

    @Override
    @Transactional
    public void leaveRoom(String roomId, Long userId) {
        ChatRoomEntity room = getRoomById(roomId);
        if (room == null) {
            return;
        }

        // 更新房间当前用户数
        int currentUsers = Math.max(0, room.getCurrentUsers() - 1);
        room.setCurrentUsers(currentUsers);
        chatRoomMapper.updateById(room);

        // 更新缓存
        cacheRoomInfo(room);

        // 从房间成员列表移除
        removeRoomMember(roomId, userId);

        log.info("用户离开聊天室: userId={}, roomId={}", userId, roomId);
    }

    @Override
    public boolean canJoinRoom(String roomId, Long userId) {
        ChatRoomEntity room = getRoomById(roomId);
        if (room == null || room.getStatus() != 1) {
            return false;
        }

        // 检查是否已在房间中
        Set<Long> roomUsers = connectionManager.getRoomUsers(roomId);
        if (roomUsers.contains(userId)) {
            return true; // 已在房间中
        }

        // 检查房间类型和权限
        if ("private".equals(room.getRoomType())) {
            // 私有房间需要检查权限
            return hasRoomPermission(roomId, userId, "join");
        } else if ("public".equals(room.getRoomType())) {
            // 公开房间检查是否被禁止
            return !isUserBannedFromRoom(roomId, userId);
        }

        return false;
    }

    /**
     * 检查用户是否有房间权限
     */
    private boolean hasRoomPermission(String roomId, Long userId, String permission) {
        try {
            String permissionKey = "chat:room:permission:" + roomId + ":" + userId;
            Object permissionObj = redisTemplate.opsForValue().get(permissionKey);

            if (permissionObj == null) {
                // 检查是否是房间创建者
                ChatRoomEntity room = getRoomById(roomId);
                return room != null && Objects.equals(room.getCreatorId(), userId);
            }

            @SuppressWarnings("unchecked")
            Set<String> permissions = JSON.parseObject(permissionObj.toString(), Set.class);
            return permissions.contains(permission) || permissions.contains("admin");

        } catch (Exception e) {
            log.error("检查房间权限失败: roomId={}, userId={}, permission={}", roomId, userId, permission, e);
            return false;
        }
    }

    /**
     * 检查用户是否被房间禁止
     */
    private boolean isUserBannedFromRoom(String roomId, Long userId) {
        try {
            String banKey = "chat:room:banned:" + roomId;
            return redisTemplate.opsForSet().isMember(banKey, userId);
        } catch (Exception e) {
            log.error("检查用户禁止状态失败: roomId={}, userId={}", roomId, userId, e);
            return false;
        }
    }

    /**
     * 设置用户房间权限
     */
    public void setUserRoomPermission(String roomId, Long userId, Set<String> permissions) {
        try {
            // 检查操作权限（只有管理员或创建者可以设置权限）
            ChatRoomEntity room = getRoomById(roomId);
            if (room == null) {
                throw new BusinessException("聊天室不存在");
            }

            String permissionKey = "chat:room:permission:" + roomId + ":" + userId;
            redisTemplate.opsForValue().set(permissionKey, JSON.toJSONString(permissions), 30, TimeUnit.DAYS);

            log.info("设置用户房间权限: roomId={}, userId={}, permissions={}", roomId, userId, permissions);

        } catch (Exception e) {
            log.error("设置用户房间权限失败: roomId={}, userId={}, permissions={}", roomId, userId, permissions, e);
            throw new BusinessException("设置权限失败");
        }
    }

    /**
     * 禁止用户进入房间
     */
    public void banUserFromRoom(String roomId, Long userId, Long operatorId, String reason) {
        try {
            // 检查操作权限
            if (!hasRoomPermission(roomId, operatorId, "ban")) {
                throw new BusinessException("无权限执行此操作");
            }

            String banKey = "chat:room:banned:" + roomId;
            redisTemplate.opsForSet().add(banKey, userId);
            redisTemplate.expire(banKey, 30, TimeUnit.DAYS);

            // 记录禁止原因
            String banInfoKey = "chat:room:ban:info:" + roomId + ":" + userId;
            Map<String, Object> banInfo = Map.of(
                "userId", userId,
                "operatorId", operatorId,
                "reason", reason != null ? reason : "违反房间规则",
                "banTime", System.currentTimeMillis()
            );
            redisTemplate.opsForValue().set(banInfoKey, JSON.toJSONString(banInfo), 30, TimeUnit.DAYS);

            // 如果用户在线，强制离开房间
            if (connectionManager.isUserOnline(userId)) {
                connectionManager.removeUserFromRoom(roomId, userId);
            }

            log.info("用户被禁止进入房间: roomId={}, userId={}, operatorId={}, reason={}",
                roomId, userId, operatorId, reason);

        } catch (Exception e) {
            log.error("禁止用户进入房间失败: roomId={}, userId={}, operatorId={}", roomId, userId, operatorId, e);
            throw new BusinessException("禁止用户失败");
        }
    }

    /**
     * 解除用户房间禁止
     */
    public void unbanUserFromRoom(String roomId, Long userId, Long operatorId) {
        try {
            // 检查操作权限
            if (!hasRoomPermission(roomId, operatorId, "ban")) {
                throw new BusinessException("无权限执行此操作");
            }

            String banKey = "chat:room:banned:" + roomId;
            redisTemplate.opsForSet().remove(banKey, userId);

            // 删除禁止信息
            String banInfoKey = "chat:room:ban:info:" + roomId + ":" + userId;
            redisTemplate.delete(banInfoKey);

            log.info("解除用户房间禁止: roomId={}, userId={}, operatorId={}", roomId, userId, operatorId);

        } catch (Exception e) {
            log.error("解除用户房间禁止失败: roomId={}, userId={}, operatorId={}", roomId, userId, operatorId, e);
            throw new BusinessException("解除禁止失败");
        }
    }

    @Override
    public List<Long> getRoomMembers(String roomId) {
        Set<Long> members = connectionManager.getRoomUsers(roomId);
        return new ArrayList<>(members);
    }

    @Override
    public int getRoomMemberCount(String roomId) {
        return connectionManager.getRoomUsers(roomId).size();
    }

    @Override
    @Transactional
    public void kickMember(String roomId, Long memberId, Long operatorId) {
        if (!isRoomAdmin(roomId, operatorId) && !isRoomCreator(roomId, operatorId)) {
            throw new BusinessException("没有权限踢出成员");
        }

        if (isRoomCreator(roomId, memberId)) {
            throw new BusinessException("不能踢出房间创建者");
        }

        // 从房间移除用户
        leaveRoom(roomId, memberId);

        // 强制断开用户连接
        if (connectionManager.isUserOnline(memberId)) {
            connectionManager.removeUserFromRoom(roomId, memberId);
        }

        log.info("用户被踢出聊天室: memberId={}, roomId={}, operatorId={}",
            memberId, roomId, operatorId);
    }

    @Override
    public void setRoomAdmin(String roomId, Long userId, Long operatorId) {
        if (!isRoomCreator(roomId, operatorId)) {
            throw new BusinessException("只有房间创建者可以设置管理员");
        }

        // 添加到管理员列表
        String key = "chat:room:admins:" + roomId;
        redisTemplate.opsForSet().add(key, userId.toString());
        redisTemplate.expire(key, 30, TimeUnit.DAYS);

        log.info("设置房间管理员: userId={}, roomId={}, operatorId={}",
            userId, roomId, operatorId);
    }

    @Override
    public boolean isRoomAdmin(String roomId, Long userId) {
        String key = "chat:room:admins:" + roomId;
        return Boolean.TRUE.equals(redisTemplate.opsForSet().isMember(key, userId.toString()));
    }

    @Override
    public boolean isRoomCreator(String roomId, Long userId) {
        ChatRoomEntity room = getRoomById(roomId);
        return room != null && room.getCreatorId().equals(userId);
    }

    /**
     * 生成房间ID
     */
    private String generateRoomId() {
        return "room_" + IdUtil.fastSimpleUUID();
    }

    /**
     * 构建房间配置
     */
    private String buildRoomConfig(ChatRoomDTO roomDTO) {
        Map<String, Object> config = new HashMap<>();
        config.put("requirePassword", roomDTO.getRequirePassword() != null ? roomDTO.getRequirePassword() : false);
        config.put("allowAnonymous", roomDTO.getAllowAnonymous() != null ? roomDTO.getAllowAnonymous() : false);
        config.put("messageRetentionDays", roomDTO.getMessageRetentionDays() != null ? roomDTO.getMessageRetentionDays() : 30);

        if (roomDTO.getPassword() != null) {
            config.put("password", roomDTO.getPassword());
        }

        return JSON.toJSONString(config);
    }

    /**
     * 缓存房间信息
     */
    private void cacheRoomInfo(ChatRoomEntity entity) {
        try {
            String key = "chat:room:info:" + entity.getRoomId();
            redisTemplate.opsForValue().set(key, JSON.toJSONString(entity), 1, TimeUnit.HOURS);
        } catch (Exception e) {
            log.error("缓存房间信息失败: roomId={}", entity.getRoomId(), e);
        }
    }

    /**
     * 获取缓存的房间信息
     */
    private ChatRoomEntity getCachedRoomInfo(String roomId) {
        try {
            String key = "chat:room:info:" + roomId;
            Object cached = redisTemplate.opsForValue().get(key);
            if (cached != null) {
                return JSON.parseObject(cached.toString(), ChatRoomEntity.class);
            }
        } catch (Exception e) {
            log.error("获取缓存房间信息失败: roomId={}", roomId, e);
        }
        return null;
    }

    /**
     * 清除房间缓存
     */
    private void clearRoomCache(String roomId) {
        try {
            String infoKey = "chat:room:info:" + roomId;
            String membersKey = "chat:room:members:" + roomId;
            String adminsKey = "chat:room:admins:" + roomId;

            redisTemplate.delete(infoKey);
            redisTemplate.delete(membersKey);
            redisTemplate.delete(adminsKey);
        } catch (Exception e) {
            log.error("清除房间缓存失败: roomId={}", roomId, e);
        }
    }

    /**
     * 添加房间成员
     */
    private void addRoomMember(String roomId, Long userId) {
        try {
            String key = "chat:room:members:" + roomId;
            redisTemplate.opsForSet().add(key, userId.toString());
            redisTemplate.expire(key, 30, TimeUnit.DAYS);
        } catch (Exception e) {
            log.error("添加房间成员失败: roomId={}, userId={}", roomId, userId, e);
        }
    }

    /**
     * 移除房间成员
     */
    private void removeRoomMember(String roomId, Long userId) {
        try {
            String key = "chat:room:members:" + roomId;
            redisTemplate.opsForSet().remove(key, userId.toString());
        } catch (Exception e) {
            log.error("移除房间成员失败: roomId={}, userId={}", roomId, userId, e);
        }
    }

    /**
     * 检查是否为房间成员
     */
    private boolean isRoomMember(String roomId, Long userId) {
        try {
            String key = "chat:room:members:" + roomId;
            return Boolean.TRUE.equals(redisTemplate.opsForSet().isMember(key, userId.toString()));
        } catch (Exception e) {
            log.error("检查房间成员失败: roomId={}, userId={}", roomId, userId, e);
            return false;
        }
    }
}
