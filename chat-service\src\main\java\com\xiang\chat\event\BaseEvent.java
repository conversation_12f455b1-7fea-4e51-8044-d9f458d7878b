package com.xiang.chat.event;

import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.UUID;

/**
 * 基础事件类
 */
@Data
public abstract class BaseEvent implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 事件ID
     */
    private String eventId;

    /**
     * 事件类型
     */
    private String eventType;

    /**
     * 事件时间
     */
    private LocalDateTime eventTime;

    /**
     * 事件来源
     */
    private String source;

    /**
     * 事件版本
     */
    private String version;

    /**
     * 用户ID
     */
    private String userId;

    /**
     * 租户ID
     */
    private String tenantId;


    private String traceId;

    /**
     * 构造函数
     */
    public BaseEvent() {
        this.eventId = UUID.randomUUID().toString();
        this.eventTime = LocalDateTime.now();
        this.eventType = this.getClass().getSimpleName();
        this.version = "1.0";
    }

    /**
     * 构造函数
     */
    public BaseEvent(String source, String userId) {
        this();
        this.source = source;
        this.userId = userId;
    }

    /**
     * 构造函数
     */
    public BaseEvent(String source, String userId, String tenantId) {
        this(source, userId);
        this.tenantId = tenantId;
    }

    public BaseEvent(String source, String userId, String tenantId, String traceId) {
        this(source, userId, tenantId);
        this.traceId = traceId;
    }
}