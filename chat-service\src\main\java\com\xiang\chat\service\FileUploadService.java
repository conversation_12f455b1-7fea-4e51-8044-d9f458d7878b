package com.xiang.chat.service;

import org.springframework.web.multipart.MultipartFile;

import java.util.Map;

/**
 * 文件上传服务接口
 */
public interface FileUploadService {
    
    /**
     * 上传聊天文件
     */
    FileUploadResult uploadChatFile(MultipartFile file, Long userId, String roomId);
    
    /**
     * 上传头像文件
     */
    FileUploadResult uploadAvatarFile(MultipartFile file, Long userId);
    
    /**
     * 删除文件
     */
    boolean deleteFile(String fileId, Long userId);
    
    /**
     * 获取文件信息
     */
    FileInfo getFileInfo(String fileId);
    
    /**
     * 获取文件下载URL
     */
    String getFileDownloadUrl(String fileId);
    
    /**
     * 检查文件是否存在
     */
    boolean fileExists(String fileId);
    
    /**
     * 文件上传结果
     */
    class FileUploadResult {
        private String fileId;
        private String fileName;
        private String originalName;
        private String fileType;
        private long fileSize;
        private String downloadUrl;
        private String thumbnailUrl;
        private Map<String, Object> metadata;
        private long uploadTime;
        
        // getters and setters
        public String getFileId() { return fileId; }
        public void setFileId(String fileId) { this.fileId = fileId; }
        public String getFileName() { return fileName; }
        public void setFileName(String fileName) { this.fileName = fileName; }
        public String getOriginalName() { return originalName; }
        public void setOriginalName(String originalName) { this.originalName = originalName; }
        public String getFileType() { return fileType; }
        public void setFileType(String fileType) { this.fileType = fileType; }
        public long getFileSize() { return fileSize; }
        public void setFileSize(long fileSize) { this.fileSize = fileSize; }
        public String getDownloadUrl() { return downloadUrl; }
        public void setDownloadUrl(String downloadUrl) { this.downloadUrl = downloadUrl; }
        public String getThumbnailUrl() { return thumbnailUrl; }
        public void setThumbnailUrl(String thumbnailUrl) { this.thumbnailUrl = thumbnailUrl; }
        public Map<String, Object> getMetadata() { return metadata; }
        public void setMetadata(Map<String, Object> metadata) { this.metadata = metadata; }
        public long getUploadTime() { return uploadTime; }
        public void setUploadTime(long uploadTime) { this.uploadTime = uploadTime; }
    }
    
    /**
     * 文件信息
     */
    class FileInfo {
        private String fileId;
        private String fileName;
        private String originalName;
        private String fileType;
        private long fileSize;
        private Long uploaderId;
        private String roomId;
        private String filePath;
        private String downloadUrl;
        private String thumbnailUrl;
        private Map<String, Object> metadata;
        private long uploadTime;
        private int downloadCount;
        private boolean isDeleted;
        
        // getters and setters
        public String getFileId() { return fileId; }
        public void setFileId(String fileId) { this.fileId = fileId; }
        public String getFileName() { return fileName; }
        public void setFileName(String fileName) { this.fileName = fileName; }
        public String getOriginalName() { return originalName; }
        public void setOriginalName(String originalName) { this.originalName = originalName; }
        public String getFileType() { return fileType; }
        public void setFileType(String fileType) { this.fileType = fileType; }
        public long getFileSize() { return fileSize; }
        public void setFileSize(long fileSize) { this.fileSize = fileSize; }
        public Long getUploaderId() { return uploaderId; }
        public void setUploaderId(Long uploaderId) { this.uploaderId = uploaderId; }
        public String getRoomId() { return roomId; }
        public void setRoomId(String roomId) { this.roomId = roomId; }
        public String getFilePath() { return filePath; }
        public void setFilePath(String filePath) { this.filePath = filePath; }
        public String getDownloadUrl() { return downloadUrl; }
        public void setDownloadUrl(String downloadUrl) { this.downloadUrl = downloadUrl; }
        public String getThumbnailUrl() { return thumbnailUrl; }
        public void setThumbnailUrl(String thumbnailUrl) { this.thumbnailUrl = thumbnailUrl; }
        public Map<String, Object> getMetadata() { return metadata; }
        public void setMetadata(Map<String, Object> metadata) { this.metadata = metadata; }
        public long getUploadTime() { return uploadTime; }
        public void setUploadTime(long uploadTime) { this.uploadTime = uploadTime; }
        public int getDownloadCount() { return downloadCount; }
        public void setDownloadCount(int downloadCount) { this.downloadCount = downloadCount; }
        public boolean isDeleted() { return isDeleted; }
        public void setDeleted(boolean deleted) { isDeleted = deleted; }
    }
}
