package com.xiang.chat.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Map;

/**
 * WebSocket消息DTO - 通用版本
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class WebSocketMessage {
    
    /**
     * 消息类型
     * auth: 认证消息
     * chat: 聊天消息
     * join_room: 加入房间
     * leave_room: 离开房间
     * ping: 心跳消息
     * pong: 心跳响应
     * error: 错误消息
     */
    private String type;
    
    /**
     * 消息数据
     */
    private Map<String, Object> data;
    
    /**
     * 时间戳
     */
    private Long timestamp;
    
    /**
     * 消息ID（可选）
     */
    private String messageId;
    
    /**
     * 发送者ID（可选）
     */
    private Long senderId;
    
    /**
     * 接收者ID（可选，私聊时使用）
     */
    private Long receiverId;
    
    /**
     * 房间ID（可选，群聊时使用）
     */
    private String roomId;
}