package com.xiang.chat.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * 聊天服务配置属性
 */
@Data
@Component
@ConfigurationProperties(prefix = "chat")
public class ChatProperties {

    /**
     * Netty配置
     */
    private Netty netty = new Netty();

    /**
     * 消息配置
     */
    private Message message = new Message();

    /**
     * 房间配置
     */
    private Room room = new Room();

    /**
     * 文件配置
     */
    private File file = new File();

    /**
     * 离线消息配置
     */
    private Offline offline = new Offline();

    @Data
    public static class Netty {
        private WebSocket websocket = new WebSocket();
        private Tcp tcp = new Tcp();

        @Data
        public static class WebSocket {
            private int port = 9090;
            private String path = "/ws";
            private int maxFrameSize = 65536;
            private int maxConnections = 10000;
        }

        @Data
        public static class Tcp {
            private int port = 9091;
            private int maxConnections = 5000;
        }
    }

    @Data
    public static class Message {
        private int maxLength = 1000;
        private int historySize = 100;
        private String offlineExpire = "7d";
    }

    @Data
    public static class Room {
        private int maxUsers = 500;
        private int maxRooms = 1000;
        private String idleTimeout = "30m";
    }

    @Data
    public static class File {
        private String maxSize = "10MB";
        private String allowedTypes = "jpg,jpeg,png,gif,pdf,doc,docx";
        private String uploadPath = "/data/chat/files";
    }

    @Data
    public static class Offline {
        private int maxMessages = 1000;
        private int expireDays = 7;
        private int sendInterval = 50;
        private int batchSize = 100;
        private int retryTimes = 3;
    }
}
