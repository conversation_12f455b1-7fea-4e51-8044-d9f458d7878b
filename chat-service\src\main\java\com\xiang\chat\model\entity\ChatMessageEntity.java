package com.xiang.chat.model.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.xiang.springcloudmybaits.config.base.BaseEntity;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * 聊天消息实体
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("chat_message")
public class ChatMessageEntity extends BaseEntity {
    
    /**
     * 消息ID（唯一标识）
     */
    @TableField("message_id")
    private String messageId;
    
    /**
     * 发送者ID
     */
    @TableField("sender_id")
    private Long senderId;
    
    /**
     * 接收者ID（私聊时使用）
     */
    @TableField("receiver_id")
    private Long receiverId;
    
    /**
     * 房间ID（群聊时使用）
     */
    @TableField("room_id")
    private String roomId;
    
    /**
     * 消息类型
     */
    @TableField("message_type")
    private String messageType;
    
    /**
     * 消息内容
     */
    @TableField("content")
    private String content;
    
    /**
     * 消息扩展数据（JSON格式）
     */
    @TableField("extra_data")
    private String extraData;
    
    /**
     * 发送时间
     */
    @TableField("send_time")
    private LocalDateTime sendTime;
    
    /**
     * 消息状态
     */
    @TableField("status")
    private Integer status;
    
    /**
     * 引用的消息ID
     */
    @TableField("reference_message_id")
    private String referenceMessageId;
    
}
