package com.xiang.chat.service;

import com.xiang.chat.dto.UserInfo;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 用户信息服务接口
 */
public interface UserInfoService {
    
    /**
     * 根据用户ID获取用户信息
     */
    UserInfo getUserInfo(Long userId);
    
    /**
     * 批量获取用户信息
     */
    Map<Long, UserInfo> getUserInfoBatch(Set<Long> userIds);
    
    /**
     * 获取用户信息列表
     */
    List<UserInfo> getUserInfoList(Set<Long> userIds);
}
