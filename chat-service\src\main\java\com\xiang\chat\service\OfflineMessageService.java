package com.xiang.chat.service;

import com.xiang.chat.dto.WebSocketMessage;
import com.xiang.chat.model.dto.ChatMessage;

import java.util.List;

/**
 * 离线消息服务接口
 */
public interface OfflineMessageService {
    
    /**
     * 存储离线消息
     */
    void storeOfflineMessage(Long userId, WebSocketMessage message);
    
    /**
     * 存储离线聊天消息
     */
    void storeOfflineChatMessage(Long userId, ChatMessage chatMessage);
    
    /**
     * 获取用户的离线消息
     */
    List<WebSocketMessage> getOfflineMessages(Long userId);
    
    /**
     * 获取用户的离线消息数量
     */
    long getOfflineMessageCount(Long userId);
    
    /**
     * 发送所有离线消息给用户
     */
    void sendOfflineMessagesToUser(Long userId);
    
    /**
     * 清除用户的离线消息
     */
    void clearOfflineMessages(Long userId);
    
    /**
     * 清除过期的离线消息
     */
    void cleanupExpiredOfflineMessages();
    
    /**
     * 标记离线消息为已发送
     */
    void markOfflineMessageAsSent(Long userId, String messageId);
    
    /**
     * 获取离线消息统计信息
     */
    OfflineMessageStats getOfflineMessageStats(Long userId);
    
    /**
     * 离线消息统计信息
     */
    class OfflineMessageStats {
        private Long userId;
        private long totalCount;
        private long unsentCount;
        private long privateMessageCount;
        private long groupMessageCount;
        private String oldestMessageTime;
        private String newestMessageTime;
        
        // getters and setters
        public Long getUserId() { return userId; }
        public void setUserId(Long userId) { this.userId = userId; }
        public long getTotalCount() { return totalCount; }
        public void setTotalCount(long totalCount) { this.totalCount = totalCount; }
        public long getUnsentCount() { return unsentCount; }
        public void setUnsentCount(long unsentCount) { this.unsentCount = unsentCount; }
        public long getPrivateMessageCount() { return privateMessageCount; }
        public void setPrivateMessageCount(long privateMessageCount) { this.privateMessageCount = privateMessageCount; }
        public long getGroupMessageCount() { return groupMessageCount; }
        public void setGroupMessageCount(long groupMessageCount) { this.groupMessageCount = groupMessageCount; }
        public String getOldestMessageTime() { return oldestMessageTime; }
        public void setOldestMessageTime(String oldestMessageTime) { this.oldestMessageTime = oldestMessageTime; }
        public String getNewestMessageTime() { return newestMessageTime; }
        public void setNewestMessageTime(String newestMessageTime) { this.newestMessageTime = newestMessageTime; }
    }
}
