package com.xiang.chat.model.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.xiang.springcloudmybaits.config.base.BaseEntity;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * 聊天房间实体
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("chat_room")
public class ChatRoomEntity extends BaseEntity {
    

    /**
     * 房间ID（唯一标识）
     */
    @TableField("room_id")
    private String roomId;
    
    /**
     * 房间名称
     */
    @TableField("room_name")
    private String roomName;
    
    /**
     * 房间描述
     */
    @TableField("description")
    private String description;
    
    /**
     * 房间类型
     * public: 公开房间
     * private: 私有房间
     * group: 群组房间
     */
    @TableField("room_type")
    private String roomType;
    
    /**
     * 房间创建者ID
     */
    @TableField("creator_id")
    private Long creatorId;
    
    /**
     * 最大用户数
     */
    @TableField("max_users")
    private Integer maxUsers;
    
    /**
     * 当前用户数
     */
    @TableField("current_users")
    private Integer currentUsers;
    
    /**
     * 房间状态
     * 0: 禁用
     * 1: 启用
     */
    @TableField("status")
    private Integer status;
    
    /**
     * 房间配置（JSON格式）
     */
    @TableField("config")
    private String config;
}
