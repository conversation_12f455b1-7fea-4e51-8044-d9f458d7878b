package com.xiang.chat.netty;

import com.xiang.chat.netty.handler.WebSocketChannelInitializer;
import io.netty.bootstrap.ServerBootstrap;
import io.netty.channel.ChannelFuture;
import io.netty.channel.ChannelOption;
import io.netty.channel.EventLoopGroup;
import io.netty.channel.nio.NioEventLoopGroup;
import io.netty.channel.socket.nio.NioServerSocketChannel;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import jakarta.annotation.PostConstruct;
import jakarta.annotation.PreDestroy;

/**
 * Netty WebSocket服务器
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class WebSocketServer {

    @Value("${netty.websocket.port:9090}")
    private int port;

    @Value("${netty.websocket.max-connections:10000}")
    private int maxConnections;

    private final WebSocketChannelInitializer channelInitializer;

    private EventLoopGroup bossGroup;
    private EventLoopGroup workerGroup;
    private ChannelFuture channelFuture;

    @PostConstruct
    public void start() {
        new Thread(this::doStart, "websocket-server").start();
    }

    private void doStart() {
        // 创建主从线程组
        bossGroup = new NioEventLoopGroup(1);
        workerGroup = new NioEventLoopGroup();

        try {
            ServerBootstrap bootstrap = new ServerBootstrap();
            bootstrap.group(bossGroup, workerGroup)
                    .channel(NioServerSocketChannel.class)
                    .childHandler(channelInitializer)
                    .option(ChannelOption.SO_BACKLOG, 2048)  // 增加连接队列大小
                    .option(ChannelOption.SO_REUSEADDR, true)
                    // .option(ChannelOption.SO_REUSEPORT, true)  // Windows 不支持 SO_REUSEPORT
                    .childOption(ChannelOption.SO_KEEPALIVE, true)
                    .childOption(ChannelOption.TCP_NODELAY, true)
                    .childOption(ChannelOption.SO_RCVBUF, 64 * 1024)  // 增加缓冲区
                    .childOption(ChannelOption.SO_SNDBUF, 64 * 1024)
                    .childOption(ChannelOption.CONNECT_TIMEOUT_MILLIS, 10000)  // 连接超时
                    .childOption(ChannelOption.SO_TIMEOUT, 10000);  // Socket 超时

            log.info("WebSocket服务器启动中，端口: {}", port);
            channelFuture = bootstrap.bind(port).sync();
            log.info("WebSocket服务器启动成功，端口: {}", port);

            // 等待服务器关闭
            channelFuture.channel().closeFuture().sync();
        } catch (Exception e) {
            log.error("WebSocket服务器启动失败", e);
        } finally {
            shutdown();
        }
    }

    @PreDestroy
    public void shutdown() {
        log.info("WebSocket服务器关闭中...");
        try {
            if (channelFuture != null) {
                channelFuture.channel().close().sync();
            }
        } catch (InterruptedException e) {
            log.error("关闭WebSocket服务器失败", e);
            Thread.currentThread().interrupt();
        } finally {
            if (workerGroup != null) {
                workerGroup.shutdownGracefully();
            }
            if (bossGroup != null) {
                bossGroup.shutdownGracefully();
            }
            log.info("WebSocket服务器已关闭");
        }
    }

    public boolean isRunning() {
        return channelFuture != null && channelFuture.channel().isActive();
    }

    public int getPort() {
        return port;
    }
}
