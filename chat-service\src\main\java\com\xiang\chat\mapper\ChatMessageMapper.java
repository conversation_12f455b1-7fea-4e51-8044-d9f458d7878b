package com.xiang.chat.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.xiang.chat.model.entity.ChatMessageEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 聊天消息Mapper
 */
@Mapper
public interface ChatMessageMapper extends BaseMapper<ChatMessageEntity> {
    
    /**
     * 获取用户未读消息数量
     */
    @Select("SELECT COUNT(*) FROM chat_message WHERE receiver_id = #{userId} AND status IN (1, 2) AND deleted = 0")
    long countUnreadMessages(@Param("userId") Long userId);
    
    /**
     * 获取房间最新消息
     */
    @Select("SELECT * FROM chat_message WHERE room_id = #{roomId} AND deleted = 0 ORDER BY send_time DESC LIMIT #{limit}")
    List<ChatMessageEntity> getLatestRoomMessages(@Param("roomId") String roomId, @Param("limit") int limit);
    
    /**
     * 获取私聊最新消息
     */
    @Select("SELECT * FROM chat_message WHERE " +
            "((sender_id = #{userId1} AND receiver_id = #{userId2}) OR " +
            "(sender_id = #{userId2} AND receiver_id = #{userId1})) " +
            "AND deleted = 0 ORDER BY send_time DESC LIMIT #{limit}")
    List<ChatMessageEntity> getLatestPrivateMessages(@Param("userId1") Long userId1, 
                                                     @Param("userId2") Long userId2, 
                                                     @Param("limit") int limit);
    
    /**
     * 获取指定时间范围内的消息
     */
    @Select("SELECT * FROM chat_message WHERE " +
            "send_time BETWEEN #{startTime} AND #{endTime} " +
            "AND deleted = 0 ORDER BY send_time ASC")
    List<ChatMessageEntity> getMessagesByTimeRange(@Param("startTime") LocalDateTime startTime,
                                                   @Param("endTime") LocalDateTime endTime);
    
    /**
     * 获取用户在指定房间的消息数量
     */
    @Select("SELECT COUNT(*) FROM chat_message WHERE sender_id = #{userId} AND room_id = #{roomId} AND deleted = 0")
    long countUserMessagesInRoom(@Param("userId") Long userId, @Param("roomId") String roomId);
    
    /**
     * 批量更新消息状态
     */
    @Select("UPDATE chat_message SET status = #{status} WHERE message_id IN " +
            "<foreach collection='messageIds' item='messageId' open='(' separator=',' close=')'>" +
            "#{messageId}" +
            "</foreach>")
    int batchUpdateMessageStatus(@Param("messageIds") List<String> messageIds, @Param("status") Integer status);
}
