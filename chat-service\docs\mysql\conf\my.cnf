[mysqld]
# 基本设置
default-storage-engine=INNODB
character-set-server=utf8mb4
collation-server=utf8mb4_unicode_ci
init_connect='SET NAMES utf8mb4'

# 连接设置
max_connections=1000
max_connect_errors=1000
wait_timeout=28800
interactive_timeout=28800

# 缓存设置
innodb_buffer_pool_size=256M
innodb_log_file_size=64M
innodb_log_buffer_size=16M
innodb_flush_log_at_trx_commit=2

# 二进制日志
log-bin=mysql-bin
binlog_format=ROW
expire_logs_days=7

# 慢查询日志
slow_query_log=1
slow_query_log_file=/var/lib/mysql/mysql-slow.log
long_query_time=2

# 时区设置
default-time-zone='+8:00'

[mysql]
default-character-set=utf8mb4

[client]
default-character-set=utf8mb4
