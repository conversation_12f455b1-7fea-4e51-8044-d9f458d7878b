# JWT认证流程测试指南

## 完整认证流程

### 1. 用户登录获取JWT token
```bash
# 通过OAuth2密码模式获取JWT token
curl -X POST http://localhost:9000/oauth2/token \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -H "Authorization: Basic Y2hhdC1jbGllbnQ6Y2hhdC1zZWNyZXQ=" \
  -d "grant_type=password&username=admin&password=admin123&scope=read write"
```

### 2. 使用JWT token验证（auth-service）
```bash
# 直接调用auth-service验证JWT
curl -X POST http://localhost:9000/api/jwt/validate \
  -H "Content-Type: application/json" \
  -d '{"token":"YOUR_JWT_TOKEN_HERE"}'
```

### 3. chat-service登录流程
1. 用户访问: http://localhost:8083/login
2. 输入用户名密码（admin/admin123）
3. chat-service通过OAuth2AuthService获取JWT token
4. 登录成功后跳转到chat页面，JWT token传递到前端

### 4. WebSocket认证流程
1. chat.html页面获取JWT token
2. 建立WebSocket连接到 ws://localhost:9090/ws
3. 发送认证消息：
```javascript
{
  "type": "auth",
  "data": {
    "token": "JWT_TOKEN",
    "tokenType": "Bearer",
    "username": "admin"
  },
  "timestamp": 1234567890
}
```
4. ConnectionManagerImpl调用JwtValidationService
5. JwtValidationService调用auth-service验证JWT
6. 返回认证结果

## 测试步骤

### 步骤1: 启动服务
```bash
# 启动基础设施
docker-compose up -d

# 启动auth-service
cd auth-service && mvn spring-boot:run

# 启动chat-service
cd chat-service && mvn spring-boot:run
```

### 步骤2: 测试OAuth2认证
```bash
# 测试获取token
curl -X POST http://localhost:9000/oauth2/token \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -H "Authorization: Basic Y2hhdC1jbGllbnQ6Y2hhdC1zZWNyZXQ=" \
  -d "grant_type=password&username=admin&password=admin123&scope=read write"
```

### 步骤3: 测试JWT验证
```bash
# 使用上一步获取的token测试验证
curl -X POST http://localhost:9000/api/jwt/validate \
  -H "Content-Type: application/json" \
  -d '{"token":"eyJ..."}'
```

### 步骤4: 测试完整登录流程
1. 访问 http://localhost:8083
2. 使用admin/admin123登录
3. 查看浏览器开发者工具，确认JWT token传递到页面
4. 观察WebSocket连接和认证过程

## 预期结果

### OAuth2 Token响应
```json
{
  "access_token": "eyJ...",
  "token_type": "Bearer",
  "expires_in": 3600,
  "refresh_token": "eyJ...",
  "scope": "read write"
}
```

### JWT验证响应
```json
{
  "valid": true,
  "user": {
    "id": 1,
    "username": "admin",
    "nickname": "系统管理员",
    "email": "<EMAIL>",
    "role": "ADMIN",
    "avatarUrl": "/avatars/admin.jpg"
  },
  "token": {
    "subject": "admin",
    "issuedAt": "2025-07-27T01:30:00Z",
    "expiresAt": "2025-07-27T02:30:00Z",
    "issuer": "http://localhost:9000"
  }
}
```

### WebSocket认证成功
```json
{
  "type": "auth_success",
  "data": {
    "userId": 1,
    "username": "admin",
    "nickname": "系统管理员",
    "avatar": "/avatars/admin.jpg",
    "serverTime": 1234567890,
    "sessionId": "abc123"
  },
  "timestamp": 1234567890
}
```

## 故障排除

### 1. JWT验证失败
- 检查token格式是否正确
- 确认token未过期
- 验证auth-service的JWT验证接口是否可访问

### 2. WebSocket认证失败
- 检查JWT token是否正确传递到前端
- 确认ConnectionManagerImpl能正常调用JwtValidationService
- 查看chat-service和auth-service的日志

### 3. 服务连接问题
- 确认auth-service在9000端口正常运行
- 检查chat-service能否访问auth-service
- 验证网络连接和防火墙设置