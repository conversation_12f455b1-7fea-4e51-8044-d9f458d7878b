package com.xiang.chat.constants;

/**
 * 消息常量
 */
public class MessageConstants {
    
    // ==================== Topic定义 ====================
    
    /**
     * 用户相关Topic
     */
    public static final String TOPIC_USER = "TOPIC_USER";
    
    /**
     * 订单相关Topic
     */
    public static final String TOPIC_ORDER = "TOPIC_ORDER";
    
    /**
     * 通知相关Topic
     */
    public static final String TOPIC_NOTIFICATION = "TOPIC_NOTIFICATION";
    
    /**
     * 日志相关Topic
     */
    public static final String TOPIC_LOG = "TOPIC_LOG";
    
    // ==================== Tag定义 ====================
    
    /**
     * 用户注册Tag
     */
    public static final String TAG_USER_REGISTERED = "USER_REGISTERED";
    
    /**
     * 用户登录Tag
     */
    public static final String TAG_USER_LOGIN = "USER_LOGIN";
    
    /**
     * 用户状态变更Tag
     */
    public static final String TAG_USER_STATUS_CHANGED = "USER_STATUS_CHANGED";
    
    /**
     * 消息转发Tag
     */
    public static final String TAG_MESSAGE_FORWARD = "MESSAGE_FORWARD";
    
    /**
     * 房间广播Tag
     */
    public static final String TAG_ROOM_BROADCAST = "ROOM_BROADCAST";
    
    /**
     * 订单创建Tag
     */
    public static final String TAG_ORDER_CREATED = "ORDER_CREATED";
    
    /**
     * 订单状态变更Tag
     */
    public static final String TAG_ORDER_STATUS_CHANGED = "ORDER_STATUS_CHANGED";
    
    /**
     * 订单支付Tag
     */
    public static final String TAG_ORDER_PAID = "ORDER_PAID";
    
    /**
     * 订单取消Tag
     */
    public static final String TAG_ORDER_CANCELLED = "ORDER_CANCELLED";
    
    // ==================== Consumer Group定义 ====================
    
    /**
     * 用户服务消费者组
     */
    public static final String GROUP_USER_SERVICE = "user-service-group";
    
    /**
     * 订单服务消费者组
     */
    public static final String GROUP_ORDER_SERVICE = "order-service-group";
    
    /**
     * 通知服务消费者组
     */
    public static final String GROUP_NOTIFICATION_SERVICE = "notification-service-group";
    
    /**
     * 日志服务消费者组
     */
    public static final String GROUP_LOG_SERVICE = "log-service-group";
    
    /**
     * 统计服务消费者组
     */
    public static final String GROUP_STATISTICS_SERVICE = "statistics-service-group";
    
    // ==================== 消息属性Key ====================
    
    /**
     * 用户ID属性Key
     */
    public static final String PROPERTY_USER_ID = "userId";
    
    /**
     * 订单ID属性Key
     */
    public static final String PROPERTY_ORDER_ID = "orderId";
    
    /**
     * 事件类型属性Key
     */
    public static final String PROPERTY_EVENT_TYPE = "eventType";
    
    /**
     * 事件来源属性Key
     */
    public static final String PROPERTY_SOURCE = "source";
    
    /**
     * 追踪ID属性Key
     */
    public static final String PROPERTY_TRACE_ID = "traceId";
    
    /**
     * 业务类型属性Key
     */
    public static final String PROPERTY_BUSINESS_TYPE = "businessType";
    
    // ==================== 延迟级别定义 ====================
    
    /**
     * 延迟1秒
     */
    public static final int DELAY_LEVEL_1S = 1;
    
    /**
     * 延迟5秒
     */
    public static final int DELAY_LEVEL_5S = 2;
    
    /**
     * 延迟10秒
     */
    public static final int DELAY_LEVEL_10S = 3;
    
    /**
     * 延迟30秒
     */
    public static final int DELAY_LEVEL_30S = 4;
    
    /**
     * 延迟1分钟
     */
    public static final int DELAY_LEVEL_1M = 5;
    
    /**
     * 延迟2分钟
     */
    public static final int DELAY_LEVEL_2M = 6;
    
    /**
     * 延迟5分钟
     */
    public static final int DELAY_LEVEL_5M = 9;
    
    /**
     * 延迟10分钟
     */
    public static final int DELAY_LEVEL_10M = 10;
    
    /**
     * 延迟30分钟
     */
    public static final int DELAY_LEVEL_30M = 16;
    
    /**
     * 延迟1小时
     */
    public static final int DELAY_LEVEL_1H = 17;
    
    // ==================== 重试配置 ====================
    
    /**
     * 默认最大重试次数
     */
    public static final int DEFAULT_MAX_RETRY_TIMES = 3;
    
    /**
     * 默认消费超时时间（分钟）
     */
    public static final int DEFAULT_CONSUME_TIMEOUT = 15;
    
    /**
     * 默认消息最大大小（字节）
     */
    public static final int DEFAULT_MAX_MESSAGE_SIZE = 4 * 1024 * 1024; // 4MB
}
