# Feign JWT认证流程测试指南

## 🏗️ 架构说明

### 调用链路
```
用户登录 → chat-service OAuth2AuthService → auth-service OAuth2 Token
    ↓
JWT Token → chat.html页面
    ↓
WebSocket认证 → ConnectionManagerImpl.authenticateUserWithDetails()
    ↓
JwtValidationService.validateToken() → AuthFeignService.validateToken()
    ↓
Feign调用 → auth-service /api/jwt/validate
    ↓
返回用户信息 → WebSocket认证成功
```

### 关键组件
- **AuthFeignService**: Feign客户端接口，调用auth-service
- **JwtValidationService**: JWT验证服务，使用Feign客户端
- **ConnectionManagerImpl**: WebSocket连接管理，使用JWT验证服务
- **OAuth2AuthService**: OAuth2客户端，获取JWT token

## 🧪 测试步骤

### 步骤1: 启动服务
```bash
# 启动基础设施
docker-compose up -d

# 启动auth-service (端口9000)
cd auth-service && mvn spring-boot:run

# 启动chat-service (端口8083)
cd chat-service && mvn spring-boot:run
```

### 步骤2: 测试OAuth2获取JWT Token
```bash
# 获取JWT token
curl -X POST http://localhost:9000/oauth2/token \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -H "Authorization: Basic Y2hhdC1jbGllbnQ6Y2hhdC1zZWNyZXQ=" \
  -d "grant_type=password&username=admin&password=admin123&scope=read write"

# 预期响应
{
  "access_token": "eyJ...",
  "token_type": "Bearer",
  "expires_in": 3600,
  "refresh_token": "eyJ...",
  "scope": "read write"
}
```

### 步骤3: 测试Feign客户端直接调用
```bash
# 测试Feign调用auth-service验证JWT
curl -X POST http://localhost:8083/test/jwt/feign-validate \
  -H "Content-Type: application/json" \
  -d '{"token":"YOUR_JWT_TOKEN_HERE"}'

# 预期响应
{
  "success": true,
  "message": "Feign调用成功",
  "response": {
    "valid": true,
    "user": {
      "id": 1,
      "username": "admin",
      "nickname": "系统管理员",
      "email": "<EMAIL>",
      "role": "ADMIN",
      "avatarUrl": "/avatars/admin.jpg"
    }
  }
}
```

### 步骤4: 测试JWT验证服务
```bash
# 测试JwtValidationService
curl -X POST http://localhost:8083/test/jwt/service-validate \
  -H "Content-Type: application/json" \
  -d '{"token":"YOUR_JWT_TOKEN_HERE"}'

# 预期响应
{
  "success": true,
  "message": "JWT验证服务调用成功",
  "result": {
    "valid": true,
    "error": null,
    "message": null,
    "userId": 1,
    "username": "admin",
    "nickname": "系统管理员",
    "avatarUrl": "/avatars/admin.jpg"
  }
}
```

### 步骤5: 测试完整登录流程
1. 访问 http://localhost:8083
2. 使用admin/admin123登录
3. 观察浏览器开发者工具，确认JWT token传递到页面
4. 观察WebSocket连接和认证过程

### 步骤6: 测试WebSocket JWT认证
1. 打开浏览器开发者工具
2. 访问聊天页面
3. 观察WebSocket消息：

**发送的认证消息**:
```json
{
  "type": "auth",
  "data": {
    "token": "eyJ...",
    "tokenType": "Bearer",
    "username": "admin"
  },
  "timestamp": **********
}
```

**预期的认证成功响应**:
```json
{
  "type": "auth_success",
  "data": {
    "userId": 1,
    "username": "admin",
    "nickname": "系统管理员",
    "avatar": "/avatars/admin.jpg",
    "serverTime": **********,
    "sessionId": "abc123"
  },
  "timestamp": **********
}
```

## 🔍 故障排除

### 1. Feign调用失败
```bash
# 检查服务发现
curl http://localhost:8083/test/jwt/health

# 检查auth-service健康状态
curl http://localhost:9000/test/health
```

**可能原因**:
- auth-service未启动或端口不正确
- Nacos服务发现配置问题
- 网络连接问题

### 2. JWT验证失败
```bash
# 检查JWT token格式
echo "YOUR_JWT_TOKEN" | base64 -d

# 测试auth-service直接验证
curl -X POST http://localhost:9000/api/jwt/validate \
  -H "Content-Type: application/json" \
  -d '{"token":"YOUR_JWT_TOKEN"}'
```

**可能原因**:
- JWT token格式错误
- Token已过期
- 用户不存在或被禁用

### 3. WebSocket认证失败
**检查步骤**:
1. 确认JWT token正确传递到前端
2. 检查WebSocket连接是否建立
3. 查看chat-service日志中的认证过程
4. 确认ConnectionManagerImpl能正常调用JwtValidationService

## 📊 监控和日志

### 关键日志位置
- **auth-service**: JWT验证日志
- **chat-service**: Feign调用和WebSocket认证日志
- **浏览器控制台**: 前端WebSocket消息

### 监控指标
- Feign调用成功率
- JWT验证响应时间
- WebSocket连接数
- 认证成功/失败次数

## 🎯 性能优化建议

### 1. 缓存优化
- 在chat-service中缓存有效的JWT验证结果
- 使用Redis缓存用户信息

### 2. 连接池优化
- 配置Feign客户端连接池
- 设置合适的超时时间

### 3. 监控告警
- 设置Feign调用失败告警
- 监控JWT验证响应时间
- 设置WebSocket连接异常告警

## 🔧 配置参数

### Feign配置
```yaml
feign:
  client:
    config:
      auth-service:
        connectTimeout: 5000
        readTimeout: 10000
        loggerLevel: basic
  hystrix:
    enabled: true
```

### 熔断配置
```yaml
hystrix:
  command:
    default:
      execution:
        isolation:
          thread:
            timeoutInMilliseconds: 10000
```

这样的架构确保了JWT验证的安全性和可靠性，同时利用了Spring Cloud的服务发现和熔断机制。